import { registerModules } from "@camped-ai/framework/utils";
import hotelManagementModule from "./hotel-management";
import { ADD_ON_SERVICE_MODULE } from "./hotel-management/add-on-service";
import customerTravellerModule, {
  CUSTOMER_TRAVELLER_MODULE,
} from "./customer-travellers";
import supplierManagementModule, {
  SUPPLIER_MANAGEMENT_MODULE,
  VendorModule,
  VENDOR_MANAGEMENT_MODULE,
} from "./vendor_management";


// Register all modules
export default registerModules({
  hotelManagement: hotelManagementModule,
  [ADD_ON_SERVICE_MODULE]:
    hotelManagementModule.resources[ADD_ON_SERVICE_MODULE],
  [CUSTOMER_TRAVELLER_MODULE]: customerTravellerModule,
  [SUPPLIER_MANAGEMENT_MODULE]: supplierManagementModule, // New primary module
  [VENDOR_MANAGEMENT_MODULE]: VendorModule, // Legacy support

});

export enum UserRole {
  ADMIN = "admin",
}

// Screen-level permissions for granular access control
export enum ScreenPermission {
  // Hotel Management
  HOTEL_MANAGEMENT_VIEW = "hotel_management:view",
  HOTEL_MANAGEMENT_CREATE = "hotel_management:create",
  HOTEL_MANAGEMENT_EDIT = "hotel_management:edit",
  HOTEL_MANAGEMENT_DELETE = "hotel_management:delete",
  HOTEL_MANAGEMENT_BULK_IMPORT = "hotel_management:bulk_import",

  // Destinations
  DESTINATIONS_VIEW = "destinations:view",
  DESTINATIONS_CREATE = "destinations:create",
  DESTINATIONS_EDIT = "destinations:edit",
  DESTINATIONS_DELETE = "destinations:delete",
  DESTINATIONS_BULK_IMPORT = "destinations:bulk_import",

  // Rooms
  ROOMS_VIEW = "rooms:view",
  ROOMS_CREATE = "rooms:create",
  ROOMS_EDIT = "rooms:edit",
  ROOMS_DELETE = "rooms:delete",
  ROOMS_BULK_IMPORT = "rooms:bulk_import",
  ROOMS_AVAILABILITY = "rooms:availability",

  // Bookings
  BOOKINGS_VIEW = "bookings:view",
  BOOKINGS_CREATE = "bookings:create",
  BOOKINGS_EDIT = "bookings:edit",
  BOOKINGS_DELETE = "bookings:delete",
  BOOKINGS_CANCEL = "bookings:cancel",

  // Add-on Services
  ADDON_SERVICES_VIEW = "addon_services:view",
  ADDON_SERVICES_CREATE = "addon_services:create",
  ADDON_SERVICES_EDIT = "addon_services:edit",
  ADDON_SERVICES_DELETE = "addon_services:delete",

  // Add-ons (Inventory)
  ADDONS_VIEW = "addons:view",
  ADDONS_EDIT = "addons:edit",
  ADDONS_BULK_UPDATE = "addons:bulk_update",
  ADDONS_EXPORT = "addons:export",

  // User Management
  USER_MANAGEMENT_VIEW = "user_management:view",
  USER_MANAGEMENT_CREATE = "user_management:create",
  USER_MANAGEMENT_EDIT = "user_management:edit",
  USER_MANAGEMENT_DELETE = "user_management:delete",
  USER_MANAGEMENT_INVITE = "user_management:invite",
  USER_MANAGEMENT_ACTIVATE = "user_management:activate",
  USER_MANAGEMENT_DEACTIVATE = "user_management:deactivate",

  // Role Management
  ROLE_MANAGEMENT_VIEW = "role_management:view",
  ROLE_MANAGEMENT_CREATE = "role_management:create",
  ROLE_MANAGEMENT_EDIT = "role_management:edit",
  ROLE_MANAGEMENT_DELETE = "role_management:delete",
  ROLE_MANAGEMENT_ASSIGN = "role_management:assign",

  // Analytics
  ANALYTICS_VIEW = "analytics:view",
  ANALYTICS_EXPORT = "analytics:export",
  ANALYTICS_STORE = "analytics:store",
  ANALYTICS_USER = "analytics:user",

  // Reports
  REPORTS_VIEW = "reports:view",
  REPORTS_EXPORT = "reports:export",
  REPORTS_HOTEL = "reports:hotel",

  // Supplier Management
  SUPPLIER_MANAGEMENT_VIEW = "supplier_management:view",
  SUPPLIER_MANAGEMENT_CREATE = "supplier_management:create",
  SUPPLIER_MANAGEMENT_EDIT = "supplier_management:edit",
  SUPPLIER_MANAGEMENT_DELETE = "supplier_management:delete",

  // Supplier Orders
  SUPPLIER_ORDERS_VIEW = "supplier_orders:view",
  SUPPLIER_ORDERS_EDIT = "supplier_orders:edit",

  // Supplier Products & Services
  SUPPLIER_PRODUCTS_SERVICES_VIEW = "supplier_products_services:view",
  SUPPLIER_PRODUCTS_SERVICES_CREATE = "supplier_products_services:create",
  SUPPLIER_PRODUCTS_SERVICES_EDIT = "supplier_products_services:edit",
  SUPPLIER_PRODUCTS_SERVICES_DELETE = "supplier_products_services:delete",

  // Supplier Offerings
  SUPPLIER_OFFERINGS_VIEW = "supplier_offerings:view",
  SUPPLIER_OFFERINGS_CREATE = "supplier_offerings:create",
  SUPPLIER_OFFERINGS_EDIT = "supplier_offerings:edit",
  SUPPLIER_OFFERINGS_DELETE = "supplier_offerings:delete",

  // Vendor Management
  VENDOR_MANAGEMENT_VIEW = "vendor_management:view",
  VENDOR_MANAGEMENT_CREATE = "vendor_management:create",
  VENDOR_MANAGEMENT_EDIT = "vendor_management:edit",
  VENDOR_MANAGEMENT_DELETE = "vendor_management:delete",

  // Fulfillment Operations
  FULFILLMENT_OPERATIONS_VIEW = "fulfillment_operations:view",
  FULFILLMENT_OPERATIONS_CREATE = "fulfillment_operations:create",
  FULFILLMENT_OPERATIONS_EDIT = "fulfillment_operations:edit",
  FULFILLMENT_OPERATIONS_DELETE = "fulfillment_operations:delete",

  // Settings
  SETTINGS_VIEW = "settings:view",
  SETTINGS_ADDON_CATEGORIES = "settings:addon_categories",
  SETTINGS_NOTIFICATION_TEMPLATES = "settings:notification_templates",

  // Storefront
  STOREFRONT_VIEW = "storefront:view",

  // Pricing
  PRICING_VIEW = "pricing:view",
  PRICING_CREATE = "pricing:create",
  PRICING_EDIT = "pricing:edit",
  PRICING_DELETE = "pricing:delete",

  // User Manual
  USER_MANUAL_VIEW = "user_manual:view",

  // Carts
  CARTS_VIEW = "carts:view",
  CARTS_DELETE = "carts:delete",

  // Subscriptions
  SUBSCRIPTIONS_VIEW = "subscriptions:view",
  SUBSCRIPTIONS_CREATE = "subscriptions:create",
  SUBSCRIPTIONS_EDIT = "subscriptions:edit",
  SUBSCRIPTIONS_DELETE = "subscriptions:delete",

  // Concierge Management
  CONCIERGE_MANAGEMENT_VIEW = "concierge_management:view",
  CONCIERGE_MANAGEMENT_CREATE = "concierge_management:create",
  CONCIERGE_MANAGEMENT_EDIT = "concierge_management:edit",
  CONCIERGE_MANAGEMENT_DELETE = "concierge_management:delete",

  // Tasks
  TASKS_VIEW = "tasks:view",
  TASKS_CREATE = "tasks:create",
  TASKS_EDIT = "tasks:edit",
  TASKS_DELETE = "tasks:delete",
  TASKS_ASSIGN = "tasks:assign",

  // Itineraries
  ITINERARIES_VIEW = "itineraries:view",
  ITINERARIES_CREATE = "itineraries:create",
  ITINERARIES_EDIT = "itineraries:edit",
  ITINERARIES_DELETE = "itineraries:delete",

  // Concierge Configuration
  CONCIERGE_CONFIG_MANAGE = "concierge_config:manage",

  // Cancellation Policies
  CANCELLATION_POLICIES_VIEW = "cancellation_policies:view",
  CANCELLATION_POLICIES_CREATE = "cancellation_policies:create",
  CANCELLATION_POLICIES_EDIT = "cancellation_policies:edit",
  CANCELLATION_POLICIES_DELETE = "cancellation_policies:delete",
}

// Role definition
export interface Role {
  id: string;
  name: string;
  description: string;
  permissions: ScreenPermission[];
  is_system_role: boolean; // true for ADMIN only
  is_active: boolean;
  created_by: string;
  updated_by?: string;
  created_at: string;
  updated_at: string;
}

// Enhanced RBAC metadata with roles support
export interface RbacMetadata {
  role?: UserRole; // Legacy support - will be deprecated
  role_id?: string; // Reference to Role
  is_active: boolean;
  custom_permissions?: ScreenPermission[]; // Override permissions for specific user
  created_by?: string;
  updated_by?: string;
  updated_at?: string;
}

export interface UserWithRbac {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  metadata?: {
    rbac?: RbacMetadata;
    [key: string]: any;
  };
  created_at: string;
  updated_at: string;
}

// Input types for role management
export interface CreateRoleInput {
  name: string;
  description: string;
  permissions: ScreenPermission[];
  created_by: string;
}

export interface UpdateRoleInput {
  id: string;
  name?: string;
  description?: string;
  permissions?: ScreenPermission[];
  is_active?: boolean;
  updated_by: string;
}

export interface SetUserRoleInput {
  user_id: string;
  role?: UserRole; // Legacy support
  role_id?: string; // New custom role support
  is_active?: boolean;
  custom_permissions?: ScreenPermission[]; // User-specific permission overrides
  updated_by?: string;
}

export interface UserRoleFilter {
  role?: UserRole;
  role_id?: string;
  is_active?: boolean;
  has_permission?: ScreenPermission;
}

// Enhanced permissions interface
export interface RolePermissions {
  canCreateHotels: boolean;
  canManageUsers: boolean;
  // Screen-level permissions
  hasPermission: (permission: ScreenPermission) => Promise<boolean>;
  hasAnyPermission: (permissions: ScreenPermission[]) => Promise<boolean>;
  hasAllPermissions: (permissions: ScreenPermission[]) => Promise<boolean>;
}

export interface UserAuthContext {
  user_id: string;
  email: string;
  role?: UserRole;
  role_id?: string;
  is_active: boolean;
  permissions: ScreenPermission[];
}

// Screen permission grouping for UI
export interface PermissionGroup {
  id: string;
  name: string;
  description: string;
  permissions: ScreenPermission[];
}

// Permission metadata for UI display
export interface PermissionMetadata {
  permission: ScreenPermission;
  name: string;
  description: string;
  group: string;
  requires_hotel_access?: boolean;
}

// Role template for quick role creation
export interface RoleTemplate {
  id: string;
  name: string;
  description: string;
  permissions: ScreenPermission[];
  suggested_for: string[];
}

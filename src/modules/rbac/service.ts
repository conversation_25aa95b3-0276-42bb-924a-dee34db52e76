import { Logger } from "@camped-ai/framework/types";
import {
  ContainerRegistrationKeys,
  MedusaError,
  MedusaErrorTypes,
  Modules,
  MedusaService,
} from "@camped-ai/framework/utils";
import { Role } from "./models/role";
import {
  UserRole,
  RbacMetadata,
  UserWithRbac,
  SetUserRoleInput,
  UserRoleFilter,
  RolePermissions,
  Role as RoleInterface,
  CreateRoleInput,
  UpdateRoleInput,
  ScreenPermission,
} from "./types";
import {
  getSystemRolePermissions,
  getAllPermissions,
  PERMISSION_GROUPS,
  ROLE_TEMPLATES,
  PERMISSION_METADATA,
} from "./permissions";

type InjectedDependencies = {
  [ContainerRegistrationKeys.LOGGER]: Logger;
};

class RbacModuleService extends MedusaService({
  Role,
}) {
  protected readonly logger_: Logger;
  protected readonly container_: any;

  // Database storage is now fully implemented

  constructor(container: InjectedDependencies) {
    super(container);
    this.container_ = container;
    this.logger_ = container[ContainerRegistrationKeys.LOGGER];
  }

  /**
   * Set role and permissions for a user
   */
  async setUserRole(data: SetUserRoleInput): Promise<UserWithRbac> {
    try {
      const userService = this.container_.resolve(Modules.USER);

      // Get existing user
      const user = await userService.retrieve(data.user_id);
      if (!user) {
        throw new MedusaError(MedusaErrorTypes.NOT_FOUND, "User not found");
      }

      // Prepare RBAC metadata
      const rbacData: RbacMetadata = {
        role: data.role,
        role_id: data.role_id,
        is_active: data.is_active ?? true,
        custom_permissions: data.custom_permissions || [],
        updated_by: data.updated_by,
        updated_at: new Date().toISOString(),
      };

      // Update user metadata
      const updatedMetadata = {
        ...user.metadata,
        rbac: rbacData,
      };

      // Update user with new metadata
      const updatedUser = await userService.update(data.user_id, {
        metadata: updatedMetadata,
      });

      return updatedUser;
    } catch (error) {
      console.error("Error in setUserRole:", error);
      throw error;
    }
  }

  /**
   * Get user with RBAC data
   */
  async getUserWithRole(userId: string): Promise<UserWithRbac> {
    try {
      const userService = this.container_.resolve(Modules.USER);
      const user = await userService.retrieve(userId);

      if (!user) {
        throw new MedusaError(MedusaErrorTypes.NOT_FOUND, "User not found");
      }

      return user;
    } catch (error) {
      console.error("Error in getUserWithRole:", error);
      throw error;
    }
  }

  /**
   * Get user's role data
   */
  getUserRole(user: UserWithRbac): RbacMetadata | null {
    return user.metadata?.rbac || null;
  }

  /**
   * Check if user has a specific role
   */
  hasRole(user: UserWithRbac, role: UserRole): boolean {
    const rbacData = this.getUserRole(user);
    return rbacData?.role === role && rbacData?.is_active === true;
  }

  /**
   * Get role permissions for a user
   */
  getRolePermissions(user: UserWithRbac): RolePermissions {
    const rbacData = this.getUserRole(user);

    if (!rbacData || !rbacData.is_active) {
      return {
        canCreateHotels: false,
        canManageUsers: false,
        hasPermission: async () => false,
        hasAnyPermission: async () => false,
        hasAllPermissions: async () => false,
      };
    }

    // Get user permissions
    const userPermissions = this.getUserPermissions(user);

    // Legacy permission mappings for backward compatibility
    const canCreateHotels = userPermissions.includes(
      ScreenPermission.HOTEL_MANAGEMENT_CREATE
    );
    const canManageUsers =
      userPermissions.includes(ScreenPermission.USER_MANAGEMENT_VIEW) &&
      userPermissions.includes(ScreenPermission.ROLE_MANAGEMENT_ASSIGN);

    return {
      canCreateHotels,
      canManageUsers,
      hasPermission: async (permission: ScreenPermission) =>
        this.hasPermission(user, permission),
      hasAnyPermission: async (permissions: ScreenPermission[]) =>
        this.hasAnyPermission(user, permissions),
      hasAllPermissions: async (permissions: ScreenPermission[]) =>
        this.hasAllPermissions(user, permissions),
    };
  }

  /**
   * List users with RBAC filtering
   */
  async listUsersWithRoles(
    filters: UserRoleFilter = {}
  ): Promise<UserWithRbac[]> {
    try {
      const userService = this.container_.resolve(Modules.USER);

      // Get all users (we'll filter by role in memory since it's in metadata)
      const users = await userService.list({});

      return users.filter((user: UserWithRbac) => {
        const rbacData = this.getUserRole(user);

        // Filter by role
        if (filters.role && rbacData?.role !== filters.role) {
          return false;
        }

        // Filter by active status
        if (
          filters.is_active !== undefined &&
          rbacData?.is_active !== filters.is_active
        ) {
          return false;
        }

        return true;
      });
    } catch (error) {
      console.error("Error in listUsersWithRoles:", error);
      throw error;
    }
  }

  /**
   * Remove role from user
   */
  async removeUserRole(userId: string): Promise<UserWithRbac> {
    try {
      const userService = this.container_.resolve(Modules.USER);
      const user = await userService.retrieve(userId);

      if (!user) {
        throw new MedusaError(MedusaErrorTypes.NOT_FOUND, "User not found");
      }

      // Remove RBAC data from metadata
      const updatedMetadata = { ...user.metadata };
      delete updatedMetadata.rbac;

      return await userService.update(userId, {
        metadata: updatedMetadata,
      });
    } catch (error) {
      console.error("Error in removeUserRole:", error);
      throw error;
    }
  }

  /**
   * Check if user is admin
   */
  isAdmin(user: UserWithRbac): boolean {
    return this.hasRole(user, UserRole.ADMIN);
  }

  /**
   * Check if user is hotel manager (now a custom role)
   * @deprecated Use hasPermission or custom role checks instead
   */
  isHotelManager(user: UserWithRbac): boolean {
    // Since hotel_manager is now a custom role, check for role_id
    const rbacData = this.getUserRole(user);
    if (!rbacData || !rbacData.is_active) return false;

    // Check if user has a custom role that might be a hotel manager role
    // This is a fallback - ideally use permission-based checks
    return rbacData.role_id !== undefined && rbacData.role_id !== null;
  }

  // ===== CUSTOM ROLE MANAGEMENT =====

  /**
   * Create default custom roles (like Hotel Manager)
   */
  async createDefaultCustomRoles(): Promise<void> {
    try {
      // Check if Hotel Manager role already exists
      const existingRoles = await this.retrieveAllCustomRoles();
      const hotelManagerExists = existingRoles.some(
        (role) => role.name === "Hotel Manager"
      );

      if (!hotelManagerExists) {
        // Create Hotel Manager role with permissions similar to the old system role
        await this.createRole({
          name: "Hotel Manager",
          description: "Hotel management access with limited permissions",
          permissions: [
            // Hotel management
            ScreenPermission.HOTEL_MANAGEMENT_VIEW,
            ScreenPermission.HOTEL_MANAGEMENT_EDIT,

            // Rooms
            ScreenPermission.ROOMS_VIEW,
            ScreenPermission.ROOMS_CREATE,
            ScreenPermission.ROOMS_EDIT,
            ScreenPermission.ROOMS_DELETE,
            ScreenPermission.ROOMS_BULK_IMPORT,
            ScreenPermission.ROOMS_AVAILABILITY,

            // Bookings
            ScreenPermission.BOOKINGS_VIEW,
            ScreenPermission.BOOKINGS_CREATE,
            ScreenPermission.BOOKINGS_EDIT,
            ScreenPermission.BOOKINGS_DELETE,
            ScreenPermission.BOOKINGS_CANCEL,

            // Add-on services
            ScreenPermission.ADDON_SERVICES_VIEW,
            ScreenPermission.ADDON_SERVICES_CREATE,
            ScreenPermission.ADDON_SERVICES_EDIT,
            ScreenPermission.ADDON_SERVICES_DELETE,

            // Analytics (limited)
            ScreenPermission.ANALYTICS_VIEW,
            ScreenPermission.ANALYTICS_STORE,

            // Fulfillment operations
            ScreenPermission.FULFILLMENT_OPERATIONS_VIEW,
            ScreenPermission.FULFILLMENT_OPERATIONS_CREATE,
            ScreenPermission.FULFILLMENT_OPERATIONS_EDIT,
            ScreenPermission.FULFILLMENT_OPERATIONS_DELETE,

            // Carts
            ScreenPermission.CARTS_VIEW,
            ScreenPermission.CARTS_EDIT,
            ScreenPermission.CARTS_DELETE,

            // User manual
            ScreenPermission.USER_MANUAL_VIEW,
          ],
          created_by: "system_setup",
        });

        this.logger_.info("Created default Hotel Manager custom role");
      }
    } catch (error) {
      this.logger_.error("Error creating default custom roles:", error);
      // Don't throw - this is not critical for setup
    }
  }

  /**
   * Create a new role
   */
  async createRole(data: CreateRoleInput): Promise<RoleInterface> {
    try {
      // For now, we'll store custom roles in a simple JSON structure
      // In a production environment, you might want to use a dedicated table
      const roleId = `custom_${Date.now()}_${Math.random()
        .toString(36)
        .substring(2, 11)}`;

      const role: RoleInterface = {
        id: roleId,
        name: data.name,
        description: data.description,
        permissions: data.permissions,
        is_system_role: false,
        is_active: true,
        created_by: data.created_by,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Store in database
      await this.createRoles({
        ...role,
        permissions: role.permissions as any, // Cast to match model type
      });

      return role;
    } catch (error) {
      console.error("Error creating custom role:", error);
      throw new MedusaError(
        MedusaErrorTypes.INVALID_DATA,
        `Failed to create custom role: ${error.message}`
      );
    }
  }

  /**
   * Update an existing role
   */
  async updateRole(data: UpdateRoleInput): Promise<RoleInterface> {
    try {
      const existingRole = await this.getRole(data.id);
      if (!existingRole) {
        throw new MedusaError(MedusaErrorTypes.NOT_FOUND, "Role not found");
      }

      if (existingRole.is_system_role) {
        throw new MedusaError(
          MedusaErrorTypes.NOT_ALLOWED,
          "Cannot modify system roles"
        );
      }

      // Check if name is changing and if new name already exists
      if (data.name && data.name !== existingRole.name) {
        const existingRoleWithName = await this.getRoleByName(data.name);
        if (existingRoleWithName && existingRoleWithName.id !== data.id) {
          throw new MedusaError(
            MedusaErrorTypes.INVALID_DATA,
            `Role with name "${data.name}" already exists`
          );
        }
      }

      const updatedRole: RoleInterface = {
        ...existingRole,
        name: data.name ?? existingRole.name,
        description: data.description ?? existingRole.description,
        permissions: data.permissions ?? existingRole.permissions,
        is_active: data.is_active ?? existingRole.is_active,
        updated_by: data.updated_by,
        updated_at: new Date().toISOString(),
      };

      // Only update fields that are provided to avoid unnecessary database operations
      const updateData: any = {
        updated_at: updatedRole.updated_at,
      };

      if (data.name !== undefined) updateData.name = updatedRole.name;
      if (data.description !== undefined) updateData.description = updatedRole.description;
      if (data.permissions !== undefined) updateData.permissions = updatedRole.permissions as any;
      if (data.is_active !== undefined) updateData.is_active = updatedRole.is_active;
      if (data.updated_by !== undefined) updateData.updated_by = updatedRole.updated_by;

      await this.updateRoles({
        selector: { id: data.id },
        data: updateData,
      });

      return updatedRole;
    } catch (error) {
      console.error("Error updating role:", error);
      console.error("Error details:", {
        message: error.message,
        type: error.type,
        stack: error.stack,
        name: error.name
      });
      throw error;
    }
  }

  /**
   * Delete a role
   */
  async deleteRole(roleId: string, _deletedBy: string): Promise<void> {
    try {
      const role = await this.getRole(roleId);
      if (!role) {
        throw new MedusaError(MedusaErrorTypes.NOT_FOUND, "Role not found");
      }

      if (role.is_system_role) {
        throw new MedusaError(
          MedusaErrorTypes.NOT_ALLOWED,
          "Cannot delete system roles"
        );
      }

      // Check if any users are assigned this role
      const usersWithRole = await this.getUsersWithRole(roleId);
      if (usersWithRole.length > 0) {
        throw new MedusaError(
          MedusaErrorTypes.NOT_ALLOWED,
          `Cannot delete role. ${usersWithRole.length} users are assigned this role.`
        );
      }

      await this.removeCustomRole(roleId);
    } catch (error) {
      console.error("Error deleting custom role:", error);
      throw error;
    }
  }

  /**
   * Get a role by ID
   */
  async getRole(roleId: string): Promise<RoleInterface | null> {
    try {
      // Check if it's a system role first
      if (roleId === UserRole.ADMIN) {
        return this.getSystemRoleAsRole(roleId as UserRole);
      }

      // TODO: Implement proper storage retrieval
      return await this.retrieveCustomRole(roleId);
    } catch (error) {
      console.error("Error getting custom role:", error);
      return null;
    }
  }

  /**
   * Get a specific role by name
   */
  async getRoleByName(roleName: string): Promise<RoleInterface | null> {
    try {
      // Check if it's a system role first
      if (roleName === "System Administrator") {
        return this.getSystemRoleAsRole(UserRole.ADMIN);
      }

      // Otherwise, try to get custom role from database by name
      const allRoles = await this.retrieveAllCustomRoles();
      return allRoles.find(role => role.name === roleName) || null;
    } catch (error) {
      console.error("Error getting role by name:", error);
      return null;
    }
  }

  /**
   * List all roles (custom method)
   */
  async listAllRoles(
    includeSystemRoles: boolean = true
  ): Promise<RoleInterface[]> {
    try {
      const customRoles = await this.retrieveAllCustomRoles();

      if (includeSystemRoles) {
        const systemRoles = [this.getSystemRoleAsRole(UserRole.ADMIN)];
        const allRoles = [...systemRoles, ...customRoles];
        return allRoles;
      }

      return customRoles;
    } catch (error) {
      console.error("Error listing custom roles:", error);
      return [];
    }
  }

  /**
   * Get users assigned to a specific role
   */
  async getUsersWithRole(roleId: string): Promise<UserWithRbac[]> {
    try {
      const userService = this.container_.resolve(Modules.USER);
      const users = await userService.list({}, { take: 1000 }); // TODO: Implement proper pagination

      return users.filter((user: any) => {
        const rbacData = user.metadata?.rbac;
        return (
          rbacData && (rbacData.role_id === roleId || rbacData.role === roleId)
        );
      });
    } catch (error) {
      console.error("Error getting users with role:", error);
      return [];
    }
  }

  /**
   * Check if user has a specific permission
   */
  async hasPermission(
    user: UserWithRbac,
    permission: ScreenPermission
  ): Promise<boolean> {
    const rbacData = this.getUserRole(user);

    if (!rbacData || !rbacData.is_active) {
      return false;
    }

    // Admins have all permissions
    if (rbacData.role === UserRole.ADMIN) {
      return true;
    }

    // Get user's effective permissions
    const userPermissions = await this.getUserEffectivePermissions(user);

    // Check if user has the specific permission
    return userPermissions.includes(permission);
  }

  /**
   * Get user's effective permissions (from role + custom permissions)
   */
  async getUserEffectivePermissions(
    user: UserWithRbac
  ): Promise<ScreenPermission[]> {
    const rbacData = this.getUserRole(user);

    if (!rbacData || !rbacData.is_active) {
      return [];
    }

    // Admins have all permissions
    if (rbacData.role === UserRole.ADMIN) {
      return this.getAllPermissions();
    }

    let permissions: ScreenPermission[] = [];

    // Get permissions from custom role
    if (rbacData.role_id) {
      try {
        const role = await this.getRole(rbacData.role_id);
        if (role && role.is_active) {
          permissions = [...role.permissions];
        }
      } catch (error) {
        console.warn(`Could not load role ${rbacData.role_id}:`, error);
      }
    }

    // Add custom permissions (user-specific overrides)
    if (rbacData.custom_permissions) {
      const customPerms = rbacData.custom_permissions.filter(
        (perm): perm is ScreenPermission =>
          Object.values(ScreenPermission).includes(perm as ScreenPermission)
      );
      permissions = [...new Set([...permissions, ...customPerms])];
    }

    return permissions;
  }

  /**
   * Check if user has any of the specified permissions
   */
  async hasAnyPermission(
    user: UserWithRbac,
    permissions: ScreenPermission[]
  ): Promise<boolean> {
    const userPermissions = await this.getUserEffectivePermissions(user);
    return permissions.some((permission) =>
      userPermissions.includes(permission)
    );
  }

  /**
   * Check if user has all of the specified permissions
   */
  async hasAllPermissions(
    user: UserWithRbac,
    permissions: ScreenPermission[]
  ): Promise<boolean> {
    const userPermissions = await this.getUserEffectivePermissions(user);
    return permissions.every((permission) =>
      userPermissions.includes(permission)
    );
  }

  /**
   * Get all permissions for a user
   */
  getUserPermissions(user: UserWithRbac): ScreenPermission[] {
    const rbacData = user.metadata?.rbac;
    if (!rbacData || !rbacData.is_active) {
      return [];
    }

    let permissions: ScreenPermission[] = [];

    // Get permissions from role
    if (rbacData.role_id) {
      // Custom role - for now, return empty permissions
      // TODO: Implement role caching or async permission checking
      permissions = [];
      this.logger_.warn(
        `Custom role permissions not available in sync context: ${rbacData.role_id}`
      );
    } else if (rbacData.role) {
      // Legacy system role
      permissions = getSystemRolePermissions(rbacData.role);
    }

    // Add custom permissions (overrides)
    if (rbacData.custom_permissions) {
      permissions = [
        ...new Set([...permissions, ...rbacData.custom_permissions]),
      ];
    }

    return permissions;
  }

  /**
   * Get permission groups for UI
   */
  getPermissionGroups() {
    return PERMISSION_GROUPS;
  }

  /**
   * Get role templates for UI
   */
  getRoleTemplates() {
    return ROLE_TEMPLATES;
  }

  /**
   * Get permission metadata for UI
   */
  getPermissionMetadata() {
    return PERMISSION_METADATA;
  }

  /**
   * Get all available permissions
   */
  getAllPermissions() {
    return getAllPermissions();
  }

  // ===== PRIVATE HELPER METHODS =====

  /**
   * Convert system role to Role format
   */
  private getSystemRoleAsRole(role: UserRole): RoleInterface {
    const permissions = getSystemRolePermissions(role);

    return {
      id: role,
      name: "System Administrator",
      description: "Full system access with all permissions",
      permissions,
      is_system_role: true,
      is_active: true,
      created_by: "system",
      created_at: "2024-01-01T00:00:00.000Z",
      updated_at: "2024-01-01T00:00:00.000Z",
    };
  }

  /**
   * Retrieve custom role from database
   */
  private async retrieveCustomRole(
    roleId: string
  ): Promise<RoleInterface | null> {
    try {
      const dbRole = await this.retrieveRole(roleId);
      if (!dbRole) return null;

      // Convert database role to interface format
      const role: RoleInterface = {
        ...dbRole,
        permissions: dbRole.permissions as any as ScreenPermission[],
        created_at: dbRole.created_at.toISOString(),
        updated_at: dbRole.updated_at.toISOString(),
      };

      this.logger_.info(`Retrieved custom role: ${roleId} - found`);
      return role;
    } catch (error) {
      this.logger_.error(`Failed to retrieve custom role: ${error.message}`);
      return null;
    }
  }

  /**
   * Retrieve all custom roles from database
   */
  private async retrieveAllCustomRoles(): Promise<RoleInterface[]> {
    try {
      // First, try to get all roles without filters to see if the method works
      const allDbRoles = await this.listRoles();

      // Filter for custom roles (is_system_role = false)
      const customDbRoles = allDbRoles.filter(
        (role) => !role.is_system_role && role.is_active
      );

      // Convert database roles to interface format
      const roles: RoleInterface[] = customDbRoles.map((dbRole) => ({
        ...dbRole,
        permissions: dbRole.permissions as any as ScreenPermission[],
        created_at: dbRole.created_at.toISOString(),
        updated_at: dbRole.updated_at.toISOString(),
      }));

      this.logger_.info(`Retrieved ${roles.length} custom roles from database`);
      return roles;
    } catch (error) {
      this.logger_.error(`Failed to retrieve custom roles: ${error.message}`);
      return [];
    }
  }

  /**
   * Remove custom role from database
   */
  private async removeCustomRole(roleId: string): Promise<void> {
    try {
      await this.deleteRoles(roleId);
      this.logger_.info(`Removed custom role: ${roleId} - success`);
    } catch (error) {
      this.logger_.error(`Failed to remove custom role: ${error.message}`);
      throw error;
    }
  }
}

export default RbacModuleService;

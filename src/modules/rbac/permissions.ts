import {
  ScreenPermission,
  PermissionGroup,
  PermissionMetadata,
  RoleTemplate,
  UserRole,
} from "./types";

/**
 * Permission metadata for UI display and validation
 */
export const PERMISSION_METADATA: Record<ScreenPermission, PermissionMetadata> =
  {
    // Hotel Management
    [ScreenPermission.HOTEL_MANAGEMENT_VIEW]: {
      permission: ScreenPermission.HOTEL_MANAGEMENT_VIEW,
      name: "View Hotels",
      description: "View hotel listings and details",
      group: "hotel_management",
      requires_hotel_access: true,
    },
    [ScreenPermission.HOTEL_MANAGEMENT_CREATE]: {
      permission: ScreenPermission.HOTEL_MANAGEMENT_CREATE,
      name: "Create Hotels",
      description: "Create new hotel properties",
      group: "hotel_management",
    },
    [ScreenPermission.HOTEL_MANAGEMENT_EDIT]: {
      permission: ScreenPermission.HOTEL_MANAGEMENT_EDIT,
      name: "Edit Hotels",
      description: "Modify hotel information and settings",
      group: "hotel_management",
      requires_hotel_access: true,
    },
    [ScreenPermission.HOTEL_MANAGEMENT_DELETE]: {
      permission: ScreenPermission.HOTEL_MANAGEMENT_DELETE,
      name: "Delete Hotels",
      description: "Remove hotel properties from the system",
      group: "hotel_management",
      requires_hotel_access: true,
    },
    [ScreenPermission.HOTEL_MANAGEMENT_BULK_IMPORT]: {
      permission: ScreenPermission.HOTEL_MANAGEMENT_BULK_IMPORT,
      name: "Bulk Import Hotels",
      description: "Import multiple hotels via CSV/Excel",
      group: "hotel_management",
    },

    // Destinations
    [ScreenPermission.DESTINATIONS_VIEW]: {
      permission: ScreenPermission.DESTINATIONS_VIEW,
      name: "View Destinations",
      description: "View destination listings and details",
      group: "destinations",
    },
    [ScreenPermission.DESTINATIONS_CREATE]: {
      permission: ScreenPermission.DESTINATIONS_CREATE,
      name: "Create Destinations",
      description: "Create new destination entries",
      group: "destinations",
    },
    [ScreenPermission.DESTINATIONS_EDIT]: {
      permission: ScreenPermission.DESTINATIONS_EDIT,
      name: "Edit Destinations",
      description: "Modify destination information",
      group: "destinations",
    },
    [ScreenPermission.DESTINATIONS_DELETE]: {
      permission: ScreenPermission.DESTINATIONS_DELETE,
      name: "Delete Destinations",
      description: "Remove destinations from the system",
      group: "destinations",
    },
    [ScreenPermission.DESTINATIONS_BULK_IMPORT]: {
      permission: ScreenPermission.DESTINATIONS_BULK_IMPORT,
      name: "Bulk Import Destinations",
      description: "Import multiple destinations via CSV/Excel",
      group: "destinations",
    },

    // Rooms
    [ScreenPermission.ROOMS_VIEW]: {
      permission: ScreenPermission.ROOMS_VIEW,
      name: "View Rooms",
      description: "View room listings and configurations",
      group: "rooms",
      requires_hotel_access: true,
    },
    [ScreenPermission.ROOMS_CREATE]: {
      permission: ScreenPermission.ROOMS_CREATE,
      name: "Create Rooms",
      description: "Create new room configurations",
      group: "rooms",
      requires_hotel_access: true,
    },
    [ScreenPermission.ROOMS_EDIT]: {
      permission: ScreenPermission.ROOMS_EDIT,
      name: "Edit Rooms",
      description: "Modify room configurations and settings",
      group: "rooms",
      requires_hotel_access: true,
    },
    [ScreenPermission.ROOMS_DELETE]: {
      permission: ScreenPermission.ROOMS_DELETE,
      name: "Delete Rooms",
      description: "Remove room configurations",
      group: "rooms",
      requires_hotel_access: true,
    },
    [ScreenPermission.ROOMS_BULK_IMPORT]: {
      permission: ScreenPermission.ROOMS_BULK_IMPORT,
      name: "Bulk Import Rooms",
      description: "Import multiple rooms via CSV/Excel",
      group: "rooms",
      requires_hotel_access: true,
    },
    [ScreenPermission.ROOMS_AVAILABILITY]: {
      permission: ScreenPermission.ROOMS_AVAILABILITY,
      name: "Manage Room Availability",
      description: "Update room availability and status",
      group: "rooms",
      requires_hotel_access: true,
    },

    // Bookings
    [ScreenPermission.BOOKINGS_VIEW]: {
      permission: ScreenPermission.BOOKINGS_VIEW,
      name: "View Bookings",
      description: "View booking listings and details",
      group: "bookings",
      requires_hotel_access: true,
    },
    [ScreenPermission.BOOKINGS_CREATE]: {
      permission: ScreenPermission.BOOKINGS_CREATE,
      name: "Create Bookings",
      description: "Create new bookings for guests",
      group: "bookings",
      requires_hotel_access: true,
    },
    [ScreenPermission.BOOKINGS_EDIT]: {
      permission: ScreenPermission.BOOKINGS_EDIT,
      name: "Edit Bookings",
      description: "Modify existing booking details",
      group: "bookings",
      requires_hotel_access: true,
    },
    [ScreenPermission.BOOKINGS_DELETE]: {
      permission: ScreenPermission.BOOKINGS_DELETE,
      name: "Delete Bookings",
      description: "Remove bookings from the system",
      group: "bookings",
      requires_hotel_access: true,
    },
    [ScreenPermission.BOOKINGS_CANCEL]: {
      permission: ScreenPermission.BOOKINGS_CANCEL,
      name: "Cancel Bookings",
      description: "Cancel guest bookings",
      group: "bookings",
      requires_hotel_access: true,
    },

    // Add-on Services
    [ScreenPermission.ADDON_SERVICES_VIEW]: {
      permission: ScreenPermission.ADDON_SERVICES_VIEW,
      name: "View Add-on Services",
      description: "View add-on service listings",
      group: "addon_services",
    },
    [ScreenPermission.ADDON_SERVICES_CREATE]: {
      permission: ScreenPermission.ADDON_SERVICES_CREATE,
      name: "Create Add-on Services",
      description: "Create new add-on services",
      group: "addon_services",
    },
    [ScreenPermission.ADDON_SERVICES_EDIT]: {
      permission: ScreenPermission.ADDON_SERVICES_EDIT,
      name: "Edit Add-on Services",
      description: "Modify add-on service details",
      group: "addon_services",
    },
    [ScreenPermission.ADDON_SERVICES_DELETE]: {
      permission: ScreenPermission.ADDON_SERVICES_DELETE,
      name: "Delete Add-on Services",
      description: "Remove add-on services",
      group: "addon_services",
    },

    // Add-ons (Inventory)
    [ScreenPermission.ADDONS_VIEW]: {
      permission: ScreenPermission.ADDONS_VIEW,
      name: "View Add-ons Inventory",
      description: "View add-ons inventory and pricing",
      group: "addons",
    },
    [ScreenPermission.ADDONS_EDIT]: {
      permission: ScreenPermission.ADDONS_EDIT,
      name: "Edit Add-ons",
      description: "Modify add-on pricing and details",
      group: "addons",
    },
    [ScreenPermission.ADDONS_BULK_UPDATE]: {
      permission: ScreenPermission.ADDONS_BULK_UPDATE,
      name: "Bulk Update Add-ons",
      description: "Perform bulk updates on add-on pricing",
      group: "addons",
    },
    [ScreenPermission.ADDONS_EXPORT]: {
      permission: ScreenPermission.ADDONS_EXPORT,
      name: "Export Add-ons",
      description: "Export add-ons data and reports",
      group: "addons",
    },

    // User Management
    [ScreenPermission.USER_MANAGEMENT_VIEW]: {
      permission: ScreenPermission.USER_MANAGEMENT_VIEW,
      name: "View Users",
      description: "View user listings and profiles",
      group: "user_management",
    },
    [ScreenPermission.USER_MANAGEMENT_CREATE]: {
      permission: ScreenPermission.USER_MANAGEMENT_CREATE,
      name: "Create Users",
      description: "Create new user accounts",
      group: "user_management",
    },
    [ScreenPermission.USER_MANAGEMENT_EDIT]: {
      permission: ScreenPermission.USER_MANAGEMENT_EDIT,
      name: "Edit Users",
      description: "Modify user profiles and settings",
      group: "user_management",
    },
    [ScreenPermission.USER_MANAGEMENT_DELETE]: {
      permission: ScreenPermission.USER_MANAGEMENT_DELETE,
      name: "Delete Users",
      description: "Remove user accounts",
      group: "user_management",
    },
    [ScreenPermission.USER_MANAGEMENT_INVITE]: {
      permission: ScreenPermission.USER_MANAGEMENT_INVITE,
      name: "Invite Users",
      description: "Send user invitations",
      group: "user_management",
    },
    [ScreenPermission.USER_MANAGEMENT_ACTIVATE]: {
      permission: ScreenPermission.USER_MANAGEMENT_ACTIVATE,
      name: "Activate Users",
      description: "Activate deactivated user accounts",
      group: "user_management",
    },
    [ScreenPermission.USER_MANAGEMENT_DEACTIVATE]: {
      permission: ScreenPermission.USER_MANAGEMENT_DEACTIVATE,
      name: "Deactivate Users",
      description: "Deactivate user accounts to prevent login",
      group: "user_management",
    },

    // Role Management
    [ScreenPermission.ROLE_MANAGEMENT_VIEW]: {
      permission: ScreenPermission.ROLE_MANAGEMENT_VIEW,
      name: "View Roles",
      description: "View role definitions and permissions",
      group: "role_management",
    },
    [ScreenPermission.ROLE_MANAGEMENT_CREATE]: {
      permission: ScreenPermission.ROLE_MANAGEMENT_CREATE,
      name: "Create Roles",
      description: "Create new custom roles",
      group: "role_management",
    },
    [ScreenPermission.ROLE_MANAGEMENT_EDIT]: {
      permission: ScreenPermission.ROLE_MANAGEMENT_EDIT,
      name: "Edit Roles",
      description: "Modify role permissions and settings",
      group: "role_management",
    },
    [ScreenPermission.ROLE_MANAGEMENT_DELETE]: {
      permission: ScreenPermission.ROLE_MANAGEMENT_DELETE,
      name: "Delete Roles",
      description: "Remove custom roles",
      group: "role_management",
    },
    [ScreenPermission.ROLE_MANAGEMENT_ASSIGN]: {
      permission: ScreenPermission.ROLE_MANAGEMENT_ASSIGN,
      name: "Assign Roles",
      description: "Assign roles to users",
      group: "role_management",
    },

    // Analytics
    [ScreenPermission.ANALYTICS_VIEW]: {
      permission: ScreenPermission.ANALYTICS_VIEW,
      name: "View Analytics",
      description: "View analytics dashboards and reports",
      group: "analytics",
    },
    [ScreenPermission.ANALYTICS_EXPORT]: {
      permission: ScreenPermission.ANALYTICS_EXPORT,
      name: "Export Analytics",
      description: "Export analytics data and reports",
      group: "analytics",
    },
    [ScreenPermission.ANALYTICS_STORE]: {
      permission: ScreenPermission.ANALYTICS_STORE,
      name: "Store Analytics",
      description: "View store-specific analytics",
      group: "analytics",
    },
    [ScreenPermission.ANALYTICS_USER]: {
      permission: ScreenPermission.ANALYTICS_USER,
      name: "User Analytics",
      description: "View user behavior analytics",
      group: "analytics",
    },

    // Reports
    [ScreenPermission.REPORTS_VIEW]: {
      permission: ScreenPermission.REPORTS_VIEW,
      name: "View Reports",
      description: "View system reports and dashboards",
      group: "reports",
    },
    [ScreenPermission.REPORTS_EXPORT]: {
      permission: ScreenPermission.REPORTS_EXPORT,
      name: "Export Reports",
      description: "Export reports to various formats",
      group: "reports",
    },
    [ScreenPermission.REPORTS_HOTEL]: {
      permission: ScreenPermission.REPORTS_HOTEL,
      name: "Hotel Reports",
      description: "View hotel-specific reports and analytics",
      group: "reports",
      requires_hotel_access: true,
    },

    // Supplier Management
    [ScreenPermission.SUPPLIER_MANAGEMENT_VIEW]: {
      permission: ScreenPermission.SUPPLIER_MANAGEMENT_VIEW,
      name: "View Suppliers",
      description: "View supplier listings and details",
      group: "supplier_management",
    },
    [ScreenPermission.SUPPLIER_MANAGEMENT_CREATE]: {
      permission: ScreenPermission.SUPPLIER_MANAGEMENT_CREATE,
      name: "Create Suppliers",
      description: "Create new supplier entries",
      group: "supplier_management",
    },
    [ScreenPermission.SUPPLIER_MANAGEMENT_EDIT]: {
      permission: ScreenPermission.SUPPLIER_MANAGEMENT_EDIT,
      name: "Edit Suppliers",
      description: "Modify supplier information",
      group: "supplier_management",
    },
    [ScreenPermission.SUPPLIER_MANAGEMENT_DELETE]: {
      permission: ScreenPermission.SUPPLIER_MANAGEMENT_DELETE,
      name: "Delete Suppliers",
      description: "Remove suppliers from the system",
      group: "supplier_management",
    },

    // Supplier Orders
    [ScreenPermission.SUPPLIER_ORDERS_VIEW]: {
      permission: ScreenPermission.SUPPLIER_ORDERS_VIEW,
      name: "View Orders",
      description: "View supplier orders and order details",
      group: "order_management",
    },
    [ScreenPermission.SUPPLIER_ORDERS_EDIT]: {
      permission: ScreenPermission.SUPPLIER_ORDERS_EDIT,
      name: "Edit Orders",
      description: "Modify supplier order information",
      group: "order_management",
    },

    // Supplier Products & Services
    [ScreenPermission.SUPPLIER_PRODUCTS_SERVICES_VIEW]: {
      permission: ScreenPermission.SUPPLIER_PRODUCTS_SERVICES_VIEW,
      name: "View Products & Services",
      description: "View supplier products and services catalog",
      group: "products_services",
    },
    [ScreenPermission.SUPPLIER_PRODUCTS_SERVICES_CREATE]: {
      permission: ScreenPermission.SUPPLIER_PRODUCTS_SERVICES_CREATE,
      name: "Create Products & Services",
      description: "Add new products and services to catalog",
      group: "products_services",
    },
    [ScreenPermission.SUPPLIER_PRODUCTS_SERVICES_EDIT]: {
      permission: ScreenPermission.SUPPLIER_PRODUCTS_SERVICES_EDIT,
      name: "Edit Products & Services",
      description: "Modify existing products and services",
      group: "products_services",
    },
    [ScreenPermission.SUPPLIER_PRODUCTS_SERVICES_DELETE]: {
      permission: ScreenPermission.SUPPLIER_PRODUCTS_SERVICES_DELETE,
      name: "Delete Products & Services",
      description: "Remove products and services from catalog",
      group: "products_services",
    },

    // Supplier Offerings
    [ScreenPermission.SUPPLIER_OFFERINGS_VIEW]: {
      permission: ScreenPermission.SUPPLIER_OFFERINGS_VIEW,
      name: "View Supplier Offerings",
      description: "View supplier offering configurations and availability",
      group: "supplier_offerings",
    },
    [ScreenPermission.SUPPLIER_OFFERINGS_CREATE]: {
      permission: ScreenPermission.SUPPLIER_OFFERINGS_CREATE,
      name: "Create Supplier Offerings",
      description: "Create new supplier offering configurations",
      group: "supplier_offerings",
    },
    [ScreenPermission.SUPPLIER_OFFERINGS_EDIT]: {
      permission: ScreenPermission.SUPPLIER_OFFERINGS_EDIT,
      name: "Edit Supplier Offerings",
      description: "Modify supplier offering configurations",
      group: "supplier_offerings",
    },
    [ScreenPermission.SUPPLIER_OFFERINGS_DELETE]: {
      permission: ScreenPermission.SUPPLIER_OFFERINGS_DELETE,
      name: "Delete Supplier Offerings",
      description: "Remove supplier offering configurations",
      group: "supplier_offerings",
    },

    // Vendor Management
    [ScreenPermission.VENDOR_MANAGEMENT_VIEW]: {
      permission: ScreenPermission.VENDOR_MANAGEMENT_VIEW,
      name: "View Vendors",
      description: "View vendor listings and details",
      group: "vendor_management",
    },
    [ScreenPermission.VENDOR_MANAGEMENT_CREATE]: {
      permission: ScreenPermission.VENDOR_MANAGEMENT_CREATE,
      name: "Create Vendors",
      description: "Create new vendor entries",
      group: "vendor_management",
    },
    [ScreenPermission.VENDOR_MANAGEMENT_EDIT]: {
      permission: ScreenPermission.VENDOR_MANAGEMENT_EDIT,
      name: "Edit Vendors",
      description: "Modify vendor information",
      group: "vendor_management",
    },
    [ScreenPermission.VENDOR_MANAGEMENT_DELETE]: {
      permission: ScreenPermission.VENDOR_MANAGEMENT_DELETE,
      name: "Delete Vendors",
      description: "Remove vendors from the system",
      group: "vendor_management",
    },

    // Fulfillment Operations
    [ScreenPermission.FULFILLMENT_OPERATIONS_VIEW]: {
      permission: ScreenPermission.FULFILLMENT_OPERATIONS_VIEW,
      name: "View Fulfillment Operations",
      description: "View fulfillment tasks and itineraries",
      group: "fulfillment_operations",
    },
    [ScreenPermission.FULFILLMENT_OPERATIONS_CREATE]: {
      permission: ScreenPermission.FULFILLMENT_OPERATIONS_CREATE,
      name: "Create Fulfillment Operations",
      description: "Create new fulfillment tasks",
      group: "fulfillment_operations",
    },
    [ScreenPermission.FULFILLMENT_OPERATIONS_EDIT]: {
      permission: ScreenPermission.FULFILLMENT_OPERATIONS_EDIT,
      name: "Edit Fulfillment Operations",
      description: "Modify fulfillment tasks and itineraries",
      group: "fulfillment_operations",
    },
    [ScreenPermission.FULFILLMENT_OPERATIONS_DELETE]: {
      permission: ScreenPermission.FULFILLMENT_OPERATIONS_DELETE,
      name: "Delete Fulfillment Operations",
      description: "Remove fulfillment tasks",
      group: "fulfillment_operations",
    },

    // Settings
    [ScreenPermission.SETTINGS_VIEW]: {
      permission: ScreenPermission.SETTINGS_VIEW,
      name: "View Settings",
      description: "View system settings and configurations",
      group: "settings",
    },
    [ScreenPermission.SETTINGS_ADDON_CATEGORIES]: {
      permission: ScreenPermission.SETTINGS_ADDON_CATEGORIES,
      name: "Manage Add-on Categories",
      description: "Manage add-on service categories",
      group: "settings",
    },
    [ScreenPermission.SETTINGS_NOTIFICATION_TEMPLATES]: {
      permission: ScreenPermission.SETTINGS_NOTIFICATION_TEMPLATES,
      name: "Manage Notification Templates",
      description: "Manage email and notification templates",
      group: "settings",
    },

    // Storefront
    [ScreenPermission.STOREFRONT_VIEW]: {
      permission: ScreenPermission.STOREFRONT_VIEW,
      name: "View Storefront",
      description: "View storefront settings and configuration",
      group: "storefront",
    },

    // Pricing
    [ScreenPermission.PRICING_VIEW]: {
      permission: ScreenPermission.PRICING_VIEW,
      name: "View Pricing",
      description: "View room rates and pricing configurations",
      group: "pricing",
    },
    [ScreenPermission.PRICING_CREATE]: {
      permission: ScreenPermission.PRICING_CREATE,
      name: "Create Pricing",
      description: "Create new room rates and pricing rules",
      group: "pricing",
    },
    [ScreenPermission.PRICING_EDIT]: {
      permission: ScreenPermission.PRICING_EDIT,
      name: "Edit Pricing",
      description: "Modify room rates and pricing configurations",
      group: "pricing",
    },
    [ScreenPermission.PRICING_DELETE]: {
      permission: ScreenPermission.PRICING_DELETE,
      name: "Delete Pricing",
      description: "Remove pricing rules and configurations",
      group: "pricing",
    },

    // User Manual
    [ScreenPermission.USER_MANUAL_VIEW]: {
      permission: ScreenPermission.USER_MANUAL_VIEW,
      name: "View User Manual",
      description: "Access user documentation and guides",
      group: "user_manual",
    },

    // Concierge Management
    [ScreenPermission.CONCIERGE_MANAGEMENT_VIEW]: {
      permission: ScreenPermission.CONCIERGE_MANAGEMENT_VIEW,
      name: "View Concierge Management",
      description: "Access concierge management dashboard",
      group: "concierge_management",
    },
    [ScreenPermission.CONCIERGE_MANAGEMENT_CREATE]: {
      permission: ScreenPermission.CONCIERGE_MANAGEMENT_CREATE,
      name: "Create Concierge Items",
      description: "Create new concierge management items",
      group: "concierge_management",
    },
    [ScreenPermission.CONCIERGE_MANAGEMENT_EDIT]: {
      permission: ScreenPermission.CONCIERGE_MANAGEMENT_EDIT,
      name: "Edit Concierge Items",
      description: "Edit existing concierge management items",
      group: "concierge_management",
    },
    [ScreenPermission.CONCIERGE_MANAGEMENT_DELETE]: {
      permission: ScreenPermission.CONCIERGE_MANAGEMENT_DELETE,
      name: "Delete Concierge Items",
      description: "Delete concierge management items",
      group: "concierge_management",
    },

    // Tasks
    [ScreenPermission.TASKS_VIEW]: {
      permission: ScreenPermission.TASKS_VIEW,
      name: "View Tasks",
      description: "View task management system",
      group: "tasks",
    },
    [ScreenPermission.TASKS_CREATE]: {
      permission: ScreenPermission.TASKS_CREATE,
      name: "Create Tasks",
      description: "Create new tasks",
      group: "tasks",
    },
    [ScreenPermission.TASKS_EDIT]: {
      permission: ScreenPermission.TASKS_EDIT,
      name: "Edit Tasks",
      description: "Edit existing tasks",
      group: "tasks",
    },
    [ScreenPermission.TASKS_DELETE]: {
      permission: ScreenPermission.TASKS_DELETE,
      name: "Delete Tasks",
      description: "Delete tasks",
      group: "tasks",
    },
    [ScreenPermission.TASKS_ASSIGN]: {
      permission: ScreenPermission.TASKS_ASSIGN,
      name: "Assign Tasks",
      description: "Assign tasks to team members",
      group: "tasks",
    },

    // Itineraries
    [ScreenPermission.ITINERARIES_VIEW]: {
      permission: ScreenPermission.ITINERARIES_VIEW,
      name: "View Itineraries",
      description: "View guest itineraries",
      group: "itineraries",
    },
    [ScreenPermission.ITINERARIES_CREATE]: {
      permission: ScreenPermission.ITINERARIES_CREATE,
      name: "Create Itineraries",
      description: "Create new guest itineraries",
      group: "itineraries",
    },
    [ScreenPermission.ITINERARIES_EDIT]: {
      permission: ScreenPermission.ITINERARIES_EDIT,
      name: "Edit Itineraries",
      description: "Edit existing itineraries",
      group: "itineraries",
    },
    [ScreenPermission.ITINERARIES_DELETE]: {
      permission: ScreenPermission.ITINERARIES_DELETE,
      name: "Delete Itineraries",
      description: "Delete guest itineraries",
      group: "itineraries",
    },

    // Concierge Configuration
    [ScreenPermission.CONCIERGE_CONFIG_MANAGE]: {
      permission: ScreenPermission.CONCIERGE_CONFIG_MANAGE,
      name: "Manage Concierge Configuration",
      description: "Manage status configuration and email templates",
      group: "concierge_config",
    },

    // Cancellation Policies
    [ScreenPermission.CANCELLATION_POLICIES_VIEW]: {
      permission: ScreenPermission.CANCELLATION_POLICIES_VIEW,
      name: "View Cancellation Policies",
      description: "View hotel cancellation policies and rules",
      group: "cancellation_policies",
      requires_hotel_access: true,
    },
    [ScreenPermission.CANCELLATION_POLICIES_CREATE]: {
      permission: ScreenPermission.CANCELLATION_POLICIES_CREATE,
      name: "Create Cancellation Policies",
      description: "Create new cancellation policies",
      group: "cancellation_policies",
      requires_hotel_access: true,
    },
    [ScreenPermission.CANCELLATION_POLICIES_EDIT]: {
      permission: ScreenPermission.CANCELLATION_POLICIES_EDIT,
      name: "Edit Cancellation Policies",
      description: "Modify existing cancellation policies",
      group: "cancellation_policies",
      requires_hotel_access: true,
    },
    [ScreenPermission.CANCELLATION_POLICIES_DELETE]: {
      permission: ScreenPermission.CANCELLATION_POLICIES_DELETE,
      name: "Delete Cancellation Policies",
      description: "Remove cancellation policies",
      group: "cancellation_policies",
      requires_hotel_access: true,
    },



    // Carts
    [ScreenPermission.CARTS_VIEW]: {
      permission: ScreenPermission.CARTS_VIEW,
      name: "View Carts",
      description: "View customer shopping carts",
      group: "carts",
    },
    [ScreenPermission.CARTS_DELETE]: {
      permission: ScreenPermission.CARTS_DELETE,
      name: "Delete Carts",
      description: "Remove customer shopping carts",
      group: "carts",
    },

    // Subscriptions
    [ScreenPermission.SUBSCRIPTIONS_VIEW]: {
      permission: ScreenPermission.SUBSCRIPTIONS_VIEW,
      name: "View Subscriptions",
      description: "View subscription listings and details",
      group: "subscriptions",
    },
    [ScreenPermission.SUBSCRIPTIONS_CREATE]: {
      permission: ScreenPermission.SUBSCRIPTIONS_CREATE,
      name: "Create Subscriptions",
      description: "Create new subscription plans",
      group: "subscriptions",
    },
    [ScreenPermission.SUBSCRIPTIONS_EDIT]: {
      permission: ScreenPermission.SUBSCRIPTIONS_EDIT,
      name: "Edit Subscriptions",
      description: "Modify subscription plans and settings",
      group: "subscriptions",
    },
    [ScreenPermission.SUBSCRIPTIONS_DELETE]: {
      permission: ScreenPermission.SUBSCRIPTIONS_DELETE,
      name: "Delete Subscriptions",
      description: "Remove subscription plans",
      group: "subscriptions",
    },
  };

/**
 * Permission groups for organizing permissions in the UI
 */
export const PERMISSION_GROUPS: PermissionGroup[] = [
  {
    id: "destinations",
    name: "Destinations",
    description: "Manage travel destinations",
    permissions: [
      ScreenPermission.DESTINATIONS_VIEW,
      ScreenPermission.DESTINATIONS_CREATE,
      ScreenPermission.DESTINATIONS_EDIT,
      ScreenPermission.DESTINATIONS_DELETE,
      ScreenPermission.DESTINATIONS_BULK_IMPORT,
    ],
  },
  {
    id: "hotel_management",
    name: "Hotel Management",
    description: "Manage hotel properties and configurations",
    permissions: [
      ScreenPermission.HOTEL_MANAGEMENT_VIEW,
      ScreenPermission.HOTEL_MANAGEMENT_CREATE,
      ScreenPermission.HOTEL_MANAGEMENT_EDIT,
      ScreenPermission.HOTEL_MANAGEMENT_DELETE,
      ScreenPermission.HOTEL_MANAGEMENT_BULK_IMPORT,
    ],
  },
  {
    id: "rooms",
    name: "Room Management",
    description: "Manage room configurations and availability",
    permissions: [
      ScreenPermission.ROOMS_VIEW,
      ScreenPermission.ROOMS_CREATE,
      ScreenPermission.ROOMS_EDIT,
      ScreenPermission.ROOMS_DELETE,
      ScreenPermission.ROOMS_BULK_IMPORT,
      ScreenPermission.ROOMS_AVAILABILITY,
    ],
  },
  {
    id: "pricing",
    name: "Pricing",
    description: "Manage room rates and pricing configurations",
    permissions: [
      ScreenPermission.PRICING_VIEW,
      ScreenPermission.PRICING_CREATE,
      ScreenPermission.PRICING_EDIT,
      ScreenPermission.PRICING_DELETE,
    ],
  },
  {
    id: "bookings",
    name: "Booking Management",
    description: "Manage guest bookings and reservations",
    permissions: [
      ScreenPermission.BOOKINGS_VIEW,
      ScreenPermission.BOOKINGS_CREATE,
      ScreenPermission.BOOKINGS_EDIT,
      ScreenPermission.BOOKINGS_DELETE,
      ScreenPermission.BOOKINGS_CANCEL,
    ],
  },
  {
    id: "carts",
    name: "Cart Management",
    description: "Manage customer shopping carts",
    permissions: [ScreenPermission.CARTS_VIEW, ScreenPermission.CARTS_DELETE],
  },
  {
    id: "subscriptions",
    name: "Subscription Management",
    description: "Manage subscription plans and billing",
    permissions: [
      ScreenPermission.SUBSCRIPTIONS_VIEW,
      ScreenPermission.SUBSCRIPTIONS_CREATE,
      ScreenPermission.SUBSCRIPTIONS_EDIT,
      ScreenPermission.SUBSCRIPTIONS_DELETE,
    ],
  },
  // {
  //   id: "addon_services",
  //   name: "Add-on Services",
  //   description: "Manage additional services and amenities",
  //   permissions: [
  //     ScreenPermission.ADDON_SERVICES_VIEW,
  //     ScreenPermission.ADDON_SERVICES_CREATE,
  //     ScreenPermission.ADDON_SERVICES_EDIT,
  //     ScreenPermission.ADDON_SERVICES_DELETE,
  //   ],
  // },
  {
    id: "addons",
    name: "Add-ons Inventory",
    description: "Manage add-ons inventory, pricing, and bulk operations",
    permissions: [
      ScreenPermission.ADDONS_VIEW,
      ScreenPermission.ADDONS_EDIT,
      ScreenPermission.ADDONS_BULK_UPDATE,
      ScreenPermission.ADDONS_EXPORT,
    ],
  },
  {
    id: "user_management",
    name: "User Management",
    description: "Manage user accounts and profiles",
    permissions: [
      ScreenPermission.USER_MANAGEMENT_VIEW,
      ScreenPermission.USER_MANAGEMENT_CREATE,
      ScreenPermission.USER_MANAGEMENT_EDIT,
      ScreenPermission.USER_MANAGEMENT_DELETE,
      ScreenPermission.USER_MANAGEMENT_INVITE,
      ScreenPermission.USER_MANAGEMENT_ACTIVATE,
      ScreenPermission.USER_MANAGEMENT_DEACTIVATE,
    ],
  },
  {
    id: "role_management",
    name: "Role Management",
    description: "Manage roles and permissions",
    permissions: [
      ScreenPermission.ROLE_MANAGEMENT_VIEW,
      ScreenPermission.ROLE_MANAGEMENT_CREATE,
      ScreenPermission.ROLE_MANAGEMENT_EDIT,
      ScreenPermission.ROLE_MANAGEMENT_DELETE,
      ScreenPermission.ROLE_MANAGEMENT_ASSIGN,
    ],
  },
  {
    id: "analytics",
    name: "Analytics & Reporting",
    description: "View analytics and generate reports",
    permissions: [
      ScreenPermission.ANALYTICS_VIEW,
      ScreenPermission.ANALYTICS_EXPORT,
      ScreenPermission.ANALYTICS_STORE,
      ScreenPermission.ANALYTICS_USER,
    ],
  },
  {
    id: "reports",
    name: "Reports & Dashboards",
    description: "Generate and view system reports",
    permissions: [
      ScreenPermission.REPORTS_VIEW,
      ScreenPermission.REPORTS_EXPORT,
      ScreenPermission.REPORTS_HOTEL,
    ],
  },
  {
    id: "supplier_management",
    name: "Supplier Management",
    description: "Manage suppliers and vendor relationships",
    permissions: [
      ScreenPermission.SUPPLIER_MANAGEMENT_VIEW,
      ScreenPermission.SUPPLIER_MANAGEMENT_CREATE,
      ScreenPermission.SUPPLIER_MANAGEMENT_EDIT,
      ScreenPermission.SUPPLIER_MANAGEMENT_DELETE,
      ScreenPermission.PRICING_VIEW,
      ScreenPermission.PRICING_EDIT,
      ScreenPermission.ROOMS_VIEW, // Required for pricing permissions
    ],
  },
  {
    id: "order_management",
    name: "Order Management",
    description: "Manage supplier orders and order processing",
    permissions: [
      ScreenPermission.SUPPLIER_ORDERS_VIEW,
      ScreenPermission.SUPPLIER_ORDERS_EDIT,
    ],
  },
  {
    id: "products_services",
    name: "Product & Services",
    description: "Manage supplier products and services catalog",
    permissions: [
      ScreenPermission.SUPPLIER_PRODUCTS_SERVICES_VIEW,
      ScreenPermission.SUPPLIER_PRODUCTS_SERVICES_CREATE,
      ScreenPermission.SUPPLIER_PRODUCTS_SERVICES_EDIT,
      ScreenPermission.SUPPLIER_PRODUCTS_SERVICES_DELETE,
    ],
  },
  {
    id: "supplier_offerings",
    name: "Supplier Offerings",
    description: "Manage supplier offering configurations and availability",
    permissions: [
      ScreenPermission.SUPPLIER_OFFERINGS_VIEW,
      ScreenPermission.SUPPLIER_OFFERINGS_CREATE,
      ScreenPermission.SUPPLIER_OFFERINGS_EDIT,
      ScreenPermission.SUPPLIER_OFFERINGS_DELETE,
    ],
  },
  {
    id: "vendor_management",
    name: "Vendor Management",
    description: "Manage vendor relationships and contracts",
    permissions: [
      ScreenPermission.VENDOR_MANAGEMENT_VIEW,
      ScreenPermission.VENDOR_MANAGEMENT_CREATE,
      ScreenPermission.VENDOR_MANAGEMENT_EDIT,
      ScreenPermission.VENDOR_MANAGEMENT_DELETE,
    ],
  },
  {
    id: "fulfillment_operations",
    name: "Fulfillment Operations",
    description: "Manage fulfillment tasks and itineraries",
    permissions: [
      ScreenPermission.FULFILLMENT_OPERATIONS_VIEW,
      ScreenPermission.FULFILLMENT_OPERATIONS_CREATE,
      ScreenPermission.FULFILLMENT_OPERATIONS_EDIT,
      ScreenPermission.FULFILLMENT_OPERATIONS_DELETE,
    ],
  },
  {
    id: "settings",
    name: "System Settings",
    description: "Configure system settings and preferences",
    permissions: [
      ScreenPermission.SETTINGS_VIEW,
      ScreenPermission.SETTINGS_ADDON_CATEGORIES,
      ScreenPermission.SETTINGS_NOTIFICATION_TEMPLATES,
    ],
  },
  {
    id: "storefront",
    name: "Storefront",
    description: "Manage storefront appearance and settings",
    permissions: [ScreenPermission.STOREFRONT_VIEW],
  },
  {
    id: "user_manual",
    name: "Documentation",
    description: "Access and manage user documentation",
    permissions: [ScreenPermission.USER_MANUAL_VIEW],
  },
  {
    id: "concierge_management",
    name: "Concierge Management",
    description: "Manage concierge operations and services",
    permissions: [
      ScreenPermission.CONCIERGE_MANAGEMENT_VIEW,
      ScreenPermission.CONCIERGE_MANAGEMENT_CREATE,
      ScreenPermission.CONCIERGE_MANAGEMENT_EDIT,
      ScreenPermission.CONCIERGE_MANAGEMENT_DELETE,
    ],
  },
  {
    id: "tasks",
    name: "Task Management",
    description: "Manage tasks and assignments",
    permissions: [
      ScreenPermission.TASKS_VIEW,
      ScreenPermission.TASKS_CREATE,
      ScreenPermission.TASKS_EDIT,
      ScreenPermission.TASKS_DELETE,
      ScreenPermission.TASKS_ASSIGN,
    ],
  },
  {
    id: "itineraries",
    name: "Itinerary Management",
    description: "Manage guest itineraries and schedules",
    permissions: [
      ScreenPermission.ITINERARIES_VIEW,
      ScreenPermission.ITINERARIES_CREATE,
      ScreenPermission.ITINERARIES_EDIT,
      ScreenPermission.ITINERARIES_DELETE,
    ],
  },
  {
    id: "concierge_config",
    name: "Concierge Configuration",
    description: "Manage concierge system configuration",
    permissions: [ScreenPermission.CONCIERGE_CONFIG_MANAGE],
  },
  {
    id: "cancellation_policies",
    name: "Cancellation Policies",
    description: "Manage hotel cancellation policies and rules",
    permissions: [
      ScreenPermission.CANCELLATION_POLICIES_VIEW,
      ScreenPermission.CANCELLATION_POLICIES_CREATE,
      ScreenPermission.CANCELLATION_POLICIES_EDIT,
      ScreenPermission.CANCELLATION_POLICIES_DELETE,
    ],
  },

];

/**
 * Role templates for quick role creation
 */
export const ROLE_TEMPLATES: RoleTemplate[] = [
  {
    id: "front_desk_manager",
    name: "Front Desk Manager",
    description: "Manages front desk operations, bookings, and guest services",
    suggested_for: ["Front desk staff", "Reception managers"],
    permissions: [
      ScreenPermission.BOOKINGS_VIEW,
      ScreenPermission.BOOKINGS_CREATE,
      ScreenPermission.BOOKINGS_EDIT,
      ScreenPermission.BOOKINGS_CANCEL,
      ScreenPermission.ROOMS_VIEW,
      ScreenPermission.ROOMS_AVAILABILITY,
      ScreenPermission.ADDON_SERVICES_VIEW,
      ScreenPermission.ADDONS_VIEW,
      ScreenPermission.CARTS_VIEW,
      ScreenPermission.USER_MANUAL_VIEW,
    ],
  },
  {
    id: "housekeeping_supervisor",
    name: "Housekeeping Supervisor",
    description:
      "Manages room status, maintenance, and housekeeping operations",
    suggested_for: ["Housekeeping staff", "Maintenance supervisors"],
    permissions: [
      ScreenPermission.ROOMS_VIEW,
      ScreenPermission.ROOMS_AVAILABILITY,
      ScreenPermission.BOOKINGS_VIEW,
      ScreenPermission.USER_MANUAL_VIEW,
    ],
  },
  {
    id: "revenue_manager",
    name: "Revenue Manager",
    description: "Manages pricing, analytics, and revenue optimization",
    suggested_for: ["Revenue managers", "Pricing analysts"],
    permissions: [
      ScreenPermission.ANALYTICS_VIEW,
      ScreenPermission.ANALYTICS_EXPORT,
      ScreenPermission.ANALYTICS_STORE,
      ScreenPermission.HOTEL_MANAGEMENT_VIEW,
      ScreenPermission.ROOMS_VIEW,
      ScreenPermission.BOOKINGS_VIEW,
      ScreenPermission.ADDONS_VIEW,
      ScreenPermission.ADDONS_EDIT,
      ScreenPermission.ADDONS_BULK_UPDATE,
      ScreenPermission.USER_MANUAL_VIEW,
    ],
  },
  {
    id: "operations_manager",
    name: "Operations Manager",
    description: "Manages day-to-day hotel operations and staff coordination",
    suggested_for: ["Operations managers", "Assistant managers"],
    permissions: [
      ScreenPermission.HOTEL_MANAGEMENT_VIEW,
      ScreenPermission.HOTEL_MANAGEMENT_EDIT,
      ScreenPermission.ROOMS_VIEW,
      ScreenPermission.ROOMS_EDIT,
      ScreenPermission.ROOMS_AVAILABILITY,
      ScreenPermission.BOOKINGS_VIEW,
      ScreenPermission.BOOKINGS_CREATE,
      ScreenPermission.BOOKINGS_EDIT,
      ScreenPermission.BOOKINGS_CANCEL,
      ScreenPermission.ADDON_SERVICES_VIEW,
      ScreenPermission.ADDON_SERVICES_EDIT,
      ScreenPermission.ADDONS_VIEW,
      ScreenPermission.ADDONS_EDIT,
      ScreenPermission.FULFILLMENT_OPERATIONS_VIEW,
      ScreenPermission.FULFILLMENT_OPERATIONS_EDIT,
      ScreenPermission.ANALYTICS_VIEW,
      ScreenPermission.USER_MANUAL_VIEW,
    ],
  },
  {
    id: "marketing_coordinator",
    name: "Marketing Coordinator",
    description:
      "Manages marketing content, storefront, and promotional activities",
    suggested_for: ["Marketing staff", "Content managers"],
    permissions: [
      ScreenPermission.STOREFRONT_VIEW,
      ScreenPermission.ADDON_SERVICES_VIEW,
      ScreenPermission.ADDON_SERVICES_EDIT,
      ScreenPermission.ANALYTICS_VIEW,
      ScreenPermission.ANALYTICS_USER,
      ScreenPermission.USER_MANUAL_VIEW,
    ],
  },
  {
    id: "procurement_manager",
    name: "Procurement Manager",
    description: "Manages suppliers, vendors, and procurement operations",
    suggested_for: ["Procurement staff", "Supply chain managers"],
    permissions: [
      ScreenPermission.SUPPLIER_MANAGEMENT_VIEW,
      ScreenPermission.SUPPLIER_MANAGEMENT_CREATE,
      ScreenPermission.SUPPLIER_MANAGEMENT_EDIT,
      ScreenPermission.VENDOR_MANAGEMENT_VIEW,
      ScreenPermission.VENDOR_MANAGEMENT_CREATE,
      ScreenPermission.VENDOR_MANAGEMENT_EDIT,
      ScreenPermission.ADDONS_VIEW,
      ScreenPermission.ADDONS_EDIT,
      ScreenPermission.PRICING_VIEW,
      ScreenPermission.PRICING_EDIT,
      ScreenPermission.ROOMS_VIEW, // Required for pricing permissions
      ScreenPermission.USER_MANUAL_VIEW,
    ],
  },
  {
    id: "guest_services_agent",
    name: "Guest Services Agent",
    description: "Handles guest inquiries, bookings, and basic services",
    suggested_for: ["Guest services staff", "Customer service agents"],
    permissions: [
      ScreenPermission.BOOKINGS_VIEW,
      ScreenPermission.BOOKINGS_CREATE,
      ScreenPermission.ROOMS_VIEW,
      ScreenPermission.ADDON_SERVICES_VIEW,
      ScreenPermission.ADDONS_VIEW,
      ScreenPermission.USER_MANUAL_VIEW,
    ],
  },
  {
    id: "read_only_viewer",
    name: "Read-Only Viewer",
    description: "View-only access to most areas for reporting and monitoring",
    suggested_for: ["Auditors", "Consultants", "Temporary staff"],
    permissions: [
      ScreenPermission.HOTEL_MANAGEMENT_VIEW,
      ScreenPermission.ROOMS_VIEW,
      ScreenPermission.BOOKINGS_VIEW,
      ScreenPermission.ADDONS_VIEW,
      ScreenPermission.ANALYTICS_VIEW,
      ScreenPermission.USER_MANUAL_VIEW,
    ],
  },
  {
    id: "concierge_manager",
    name: "Concierge Manager",
    description: "Manages concierge operations, tasks, and guest services",
    suggested_for: ["Concierge staff", "Guest services managers", "VIP coordinators"],
    permissions: [
      ScreenPermission.CONCIERGE_MANAGEMENT_VIEW,
      ScreenPermission.CONCIERGE_MANAGEMENT_CREATE,
      ScreenPermission.CONCIERGE_MANAGEMENT_EDIT,
      ScreenPermission.CONCIERGE_MANAGEMENT_DELETE,
      ScreenPermission.TASKS_VIEW,
      ScreenPermission.TASKS_CREATE,
      ScreenPermission.TASKS_EDIT,
      ScreenPermission.TASKS_DELETE,
      ScreenPermission.TASKS_ASSIGN,
      ScreenPermission.ITINERARIES_VIEW,
      ScreenPermission.ITINERARIES_CREATE,
      ScreenPermission.ITINERARIES_EDIT,
      ScreenPermission.ITINERARIES_DELETE,
      ScreenPermission.CONCIERGE_CONFIG_MANAGE,
      ScreenPermission.BOOKINGS_VIEW,
      ScreenPermission.BOOKINGS_EDIT,
      ScreenPermission.ROOMS_VIEW,
      ScreenPermission.USER_MANUAL_VIEW,
    ],
  },
];

/**
 * System role definitions with their default permissions
 */
export const SYSTEM_ROLES: Record<UserRole, ScreenPermission[]> = {
  [UserRole.ADMIN]: [
    // Full access to everything
    ...Object.values(ScreenPermission),
  ],
};

/**
 * Permission hierarchy mapping for automatic parent selection
 * When a child permission is selected, all parent permissions are automatically selected
 */
export const PERMISSION_HIERARCHY: Record<string, string | null> = {
  [ScreenPermission.ROOMS_VIEW]: ScreenPermission.HOTEL_MANAGEMENT_VIEW,
  [ScreenPermission.HOTEL_MANAGEMENT_VIEW]: ScreenPermission.DESTINATIONS_VIEW,
  [ScreenPermission.DESTINATIONS_VIEW]: null,

  // Add other hierarchical relationships as needed
  [ScreenPermission.ROOMS_CREATE]: ScreenPermission.HOTEL_MANAGEMENT_VIEW,
  [ScreenPermission.ROOMS_EDIT]: ScreenPermission.HOTEL_MANAGEMENT_VIEW,
  [ScreenPermission.ROOMS_DELETE]: ScreenPermission.HOTEL_MANAGEMENT_VIEW,
  [ScreenPermission.ROOMS_BULK_IMPORT]: ScreenPermission.HOTEL_MANAGEMENT_VIEW,
  [ScreenPermission.ROOMS_AVAILABILITY]: ScreenPermission.HOTEL_MANAGEMENT_VIEW,

  [ScreenPermission.HOTEL_MANAGEMENT_CREATE]:
    ScreenPermission.DESTINATIONS_VIEW,
  [ScreenPermission.HOTEL_MANAGEMENT_EDIT]: ScreenPermission.DESTINATIONS_VIEW,
  [ScreenPermission.HOTEL_MANAGEMENT_DELETE]:
    ScreenPermission.DESTINATIONS_VIEW,
  [ScreenPermission.HOTEL_MANAGEMENT_BULK_IMPORT]:
    ScreenPermission.DESTINATIONS_VIEW,

  // Pricing permissions require room access
  [ScreenPermission.PRICING_VIEW]: ScreenPermission.ROOMS_VIEW,
  [ScreenPermission.PRICING_CREATE]: ScreenPermission.ROOMS_VIEW,
  [ScreenPermission.PRICING_EDIT]: ScreenPermission.ROOMS_VIEW,
  [ScreenPermission.PRICING_DELETE]: ScreenPermission.ROOMS_VIEW,
};

/**
 * Get all parent permissions for a given permission
 */
export const getAllParents = (
  permission: ScreenPermission
): ScreenPermission[] => {
  const parents: ScreenPermission[] = [];
  let current = PERMISSION_HIERARCHY[permission];
  while (current) {
    parents.push(current as ScreenPermission);
    current = PERMISSION_HIERARCHY[current];
  }
  return parents;
};

/**
 * Get all children for a given permission
 */
export const getAllChildren = (
  permission: ScreenPermission
): ScreenPermission[] => {
  const children: ScreenPermission[] = [];
  Object.entries(PERMISSION_HIERARCHY).forEach(([child, parent]) => {
    if (parent === permission) {
      children.push(child as ScreenPermission);
      // Recursively add grandchildren
      children.push(...getAllChildren(child as ScreenPermission));
    }
  });
  return children;
};

/**
 * Apply hierarchical permission logic when toggling permissions
 * When selecting a permission, automatically select all required parents
 * When deselecting a permission, automatically deselect all dependent children
 */
export const applyPermissionHierarchy = (
  currentPermissions: ScreenPermission[],
  toggledPermission: ScreenPermission,
  isSelecting: boolean
): ScreenPermission[] => {
  let newPermissions = [...currentPermissions];

  if (isSelecting) {
    // Add the permission if not already present
    if (!newPermissions.includes(toggledPermission)) {
      newPermissions.push(toggledPermission);
    }

    // Automatically add all parent permissions
    const parents = getAllParents(toggledPermission);
    parents.forEach((parent) => {
      if (!newPermissions.includes(parent)) {
        newPermissions.push(parent);
      }
    });
  } else {
    // Remove the permission
    newPermissions = newPermissions.filter((p) => p !== toggledPermission);

    // Automatically remove all child permissions
    const children = getAllChildren(toggledPermission);
    children.forEach((child) => {
      newPermissions = newPermissions.filter((p) => p !== child);
    });
  }

  return newPermissions;
};

/**
 * Helper functions for permission management
 */
export const getPermissionsByGroup = (groupId: string): ScreenPermission[] => {
  const group = PERMISSION_GROUPS.find((g) => g.id === groupId);
  return group ? group.permissions : [];
};

export const getPermissionMetadata = (
  permission: ScreenPermission
): PermissionMetadata => {
  return PERMISSION_METADATA[permission];
};

export const getAllPermissions = (): ScreenPermission[] => {
  return Object.values(ScreenPermission);
};

export const getSystemRolePermissions = (
  role: UserRole
): ScreenPermission[] => {
  return SYSTEM_ROLES[role] || [];
};

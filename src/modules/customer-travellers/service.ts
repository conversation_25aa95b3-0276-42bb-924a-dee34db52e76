import { MedusaService, MedusaError } from "@camped-ai/framework/utils";
import { CustomerTraveller } from "./models/customer-traveller";
import {
  CreateCustomerTravellerInput,
  UpdateCustomerTravellerInput,
  CustomerTravellerData,
  ListCustomerTravellersFilters,
} from "./types";

class CustomerTravellerModuleService extends MedusaService({
  CustomerTraveller,
}) {
  /**
   * Get all travellers for a specific customer
   */
  async getCustomerTravellers(
    customerId: string,
    filters?: Omit<ListCustomerTravellersFilters, "customer_id">
  ): Promise<CustomerTravellerData[]> {
    return await this.listCustomerTravellers({
      customer_id: customerId,
      ...filters,
    });
  }

  /**
   * Get a single traveller by ID (with customer ownership check)
   */
  async getCustomerTraveller(
    travellerId: string,
    customerId: string
  ): Promise<CustomerTravellerData> {
    // Use the auto-generated retrieve method from MedusaService
    const travellers = await this.listCustomerTravellers({ id: travellerId });

    if (travellers.length === 0) {
      throw new MedusaError(
        MedusaError.Types.NOT_FOUND,
        "Traveller not found"
      );
    }

    const traveller = travellers[0];

    if (traveller.customer_id !== customerId) {
      throw new MedusaError(
        MedusaError.Types.NOT_FOUND,
        "Traveller not found or access denied"
      );
    }

    return traveller;
  }

  /**
   * Create a new traveller for a customer
   */
  async createCustomerTraveller(
    data: CreateCustomerTravellerInput
  ): Promise<CustomerTravellerData> {
    // Validate that we don't create duplicate travellers
    const existingTravellers = await this.listCustomerTravellers({
      customer_id: data.customer_id,
      first_name: data.first_name,
      last_name: data.last_name,
      date_of_birth: data.date_of_birth,
    });

    if (existingTravellers.length > 0) {
      throw new MedusaError(
        MedusaError.Types.DUPLICATE_ERROR,
        "A traveller with the same name and date of birth already exists"
      );
    }

    const travellers = await this.createCustomerTravellers([data]);
    return travellers[0];
  }

  /**
   * Update a traveller (only if it belongs to the customer)
   */
  async updateCustomerTraveller(
    travellerId: string,
    customerId: string,
    data: UpdateCustomerTravellerInput
  ): Promise<CustomerTravellerData> {
    // First verify the traveller belongs to the customer
    await this.getCustomerTraveller(travellerId, customerId);

    const travellers = await this.updateCustomerTravellers([{
      id: travellerId,
      ...data,
    }]);
    return travellers[0];
  }

  /**
   * Delete a traveller (only if it belongs to the customer)
   */
  async deleteCustomerTraveller(
    travellerId: string,
    customerId: string
  ): Promise<void> {
    // First verify the traveller belongs to the customer
    await this.getCustomerTraveller(travellerId, customerId);

    await this.deleteCustomerTravellers([travellerId]);
  }

  /**
   * Get travellers count for a customer
   */
  async getCustomerTravellersCount(customerId: string): Promise<number> {
    const [, count] = await this.listAndCountCustomerTravellers({
      customer_id: customerId,
    });
    return count;
  }
}

export default CustomerTravellerModuleService;

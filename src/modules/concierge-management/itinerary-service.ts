import { MedusaService } from "@camped-ai/framework/utils";
import { MedusaContainer } from "@camped-ai/framework/types";
import { EntityManager } from "typeorm";
import {
  Itinerary,
  ItineraryDay,
  ItineraryEvent,
} from "./models/itinerary";

/**
 * Service for managing itineraries, days, and events
 */
class ItineraryService extends MedusaService({
  Itinerary,
  ItineraryDay,
  ItineraryEvent,
}) {
  protected container_: MedusaContainer;
  protected activeManager_: EntityManager;

  constructor(container: MedusaContainer) {
    super(container);
    this.container_ = container;
    this.activeManager_ = this.container_["manager" as any];
  }

  /**
   * Create an itinerary from a booking
   */
  async createItineraryFromBooking(
    bookingId: string,
    bookingStartDate: Date,
    createdBy?: string
  ) {
    // Check if itinerary already exists for this booking
    const existingItinerary = await this.listAllItineraries({
      booking_id: bookingId,
    });

    if (existingItinerary.length > 0) {
      throw new Error("Itinerary already exists for this booking");
    }

    // Create the itinerary
    const itinerary = await this.createItineraries({
      booking_id: bookingId,
      title: null,
      status: "DRAFT",
      created_by: createdBy,
    });

    // Create the first day using booking start date
    const firstDay = await this.createItineraryDays({
      itinerary_id: itinerary.id,
      date: bookingStartDate,
      title: null,
      sort_order: 1,
    });

    return {
      itinerary,
      firstDay,
    };
  }

  /**
   * Get full itinerary with days and events
   */
  async getItineraryWithDaysAndEvents(itineraryId: string) {
    try {
      const itinerary = await this.retrieveItinerary(itineraryId);

      if (!itinerary) {
        throw new Error("Itinerary not found");
      }

      const days = await this.listItineraryDays(
        { itinerary_id: itineraryId },
        { order: { sort_order: "ASC" } }
      );

      // Ensure days is always an array
      const daysList = Array.isArray(days) ? days : [];

      const daysWithEvents = await Promise.all(
        daysList.map(async (day) => {
          try {
            const events = await this.listItineraryEvents(
              { day_id: day.id },
              { order: { start_time: "ASC" } }
            );
            return {
              ...day,
              events: Array.isArray(events) ? events : [],
            };
          } catch (error) {
            console.error(`Error fetching events for day ${day.id}:`, error);
            return {
              ...day,
              events: [],
            };
          }
        })
      );

      return {
        ...itinerary,
        days: daysWithEvents,
      };
    } catch (error) {
      console.error("Error in getItineraryWithDaysAndEvents:", error);
      throw error;
    }
  }

  /**
   * Add a new day to an itinerary
   */
  async addDayToItinerary(
    itineraryId: string,
    date: Date,
    title?: string
  ) {
    // Check if date already exists
    const existingDay = await this.listItineraryDays({
      itinerary_id: itineraryId,
      date: date,
    });

    if (existingDay.length > 0) {
      throw new Error("A day with this date already exists");
    }

    // Get the next sort order
    const existingDays = await this.listItineraryDays(
      { itinerary_id: itineraryId },
      { order: { sort_order: "DESC" }, take: 1 }
    );

    const nextSortOrder = existingDays.length > 0 ? existingDays[0].sort_order + 1 : 1;

    return await this.createItineraryDays({
      itinerary_id: itineraryId,
      date: date,
      title: title,
      sort_order: nextSortOrder,
    });
  }

  /**
   * Reorder days in an itinerary
   */
  async reorderDays(dayUpdates: Array<{ id: string; sort_order: number }>) {
    const updatePromises = dayUpdates.map(({ id, sort_order }) =>
      this.updateItineraryDays({ id, sort_order })
    );

    return await Promise.all(updatePromises);
  }

  /**
   * Add an event to a day
   */
  async addEventToDay(dayId: string, eventData: {
    category: string;
    type?: string;
    title: string;
    notes?: string;
    start_time?: string;
    end_time?: string;
    duration?: string;
    timezone?: string;
    details?: Record<string, any>;
    price?: number;
    currency?: string;
    media?: string[];
    attachments?: string[];
    people?: string[];
  }) {
    return await this.createItineraryEvents({
      day_id: dayId,
      ...eventData,
      category: eventData.category as any, // Temp fix for category type
    });
  }

  /**
   * Update an event
   */
  async updateEvent(eventId: string, eventData: Partial<{
    category: string;
    type: string;
    title: string;
    notes: string;
    start_time: string;
    end_time: string;
    duration: string;
    timezone: string;
    details: Record<string, any>;
    price: number;
    currency: string;
    media: string[];
    attachments: string[];
    people: string[];
  }>) {
    return await this.updateItineraryEvents({
      id: eventId,
      ...eventData,
      category: eventData.category as any,
    });
  }

  /**
   * Delete an event
   */
  async deleteEvent(eventId: string) {
    return await this.deleteItineraryEvents(eventId);
  }

  /**
   * Delete a day and all its events
   */
  async deleteDay(dayId: string) {
    // First delete all events for this day
    const events = await this.listItineraryEvents({ day_id: dayId });
    
    if (events.length > 0) {
      const eventIds = events.map(event => event.id);
      await this.deleteItineraryEvents(eventIds);
    }

    // Then delete the day
    return await this.deleteItineraryDays(dayId);
  }

  /**
   * Get events for a specific day
   */
  async getEventsForDay(dayId: string) {
    return await this.listItineraryEvents(
      { day_id: dayId },
      { order: { start_time: "ASC" } }
    );
  }

  /**
   * Update itinerary status
   */
  async updateItineraryStatus(itineraryId: string, status: "DRAFT" | "FINALIZED") {
    return await this.updateItineraries({ id: itineraryId, status });
  }

  /**
   * List itineraries with filtering
   */
  async listAllItineraries(filters: any = {}) {
    try {
      console.log("listAllItineraries called with filters:", filters);

      // Use the repository directly to get itineraries
      // Use the list method to get itineraries
      const itineraries = await this.listItineraries(filters, {
        order: { created_at: "DESC" },
      });

      // For each itinerary, fetch its days and events
      const itinerariesWithDetails = await Promise.all(
        itineraries.map(async (itinerary) => {
          try {
            const days = await this.listItineraryDays(
              { itinerary_id: itinerary.id },
              { order: { sort_order: "ASC" } }
            );

            const daysList = Array.isArray(days) ? days : [];

            const daysWithEvents = await Promise.all(
              daysList.map(async (day) => {
                try {
                  const events = await this.listItineraryEvents(
                    { day_id: day.id },
                    { order: { start_time: "ASC" } }
                  );
                  return {
                    ...day,
                    events: Array.isArray(events) ? events : [],
                  };
                } catch (error) {
                  console.error(`Error fetching events for day ${day.id}:`, error);
                  return {
                    ...day,
                    events: [],
                  };
                }
              })
            );

            return {
              ...itinerary,
              days: daysWithEvents,
            };
          } catch (error) {
            console.error(`Error fetching days for itinerary ${itinerary.id}:`, error);
            return {
              ...itinerary,
              days: [],
            };
          }
        })
      );

      console.log("Found itineraries:", itinerariesWithDetails?.length || 0);
      return itinerariesWithDetails;
    } catch (error) {
      console.error("Error in listAllItineraries:", error);
      throw error;
    }
  }

  /**
   * Get itinerary by booking ID
   */
  async getItineraryByBookingId(bookingId: string) {
    const itineraries = await this.listAllItineraries({ booking_id: bookingId });
    return itineraries.length > 0 ? itineraries[0] : null;
  }
}

export default ItineraryService;

import { model } from "@camped-ai/framework/utils";

/**
 * Itinerary Model
 * 
 * Represents the main itinerary associated with a booking.
 * One booking can have one itinerary.
 */
export const Itinerary = model.define("itinerary", {
  id: model.id({ prefix: "itin" }).primaryKey(),
  booking_id: model.text().index(), // Reference to booking
  title: model.text().nullable(),
  status: model.enum([
    "DRAFT",
    "FINALIZED"
  ]).default("DRAFT"),
  created_by: model.text().nullable(), // User ID
})
.indexes([
  {
    name: "IDX_itinerary_booking_id",
    on: ["booking_id"],
    unique: true, // One itinerary per booking
    where: "deleted_at IS NULL",
  },
]);

/**
 * Itinerary Day Model
 * 
 * Represents individual days within an itinerary.
 * Each day can contain multiple events.
 */
export const ItineraryDay = model.define("itinerary_day", {
  id: model.id({ prefix: "itin_day" }).primaryKey(),
  itinerary_id: model.text().index(), // Reference to itinerary
  date: model.dateTime(),
  title: model.text().nullable(), // e.g., "Welcome to Los Angeles"
  sort_order: model.number().default(0),
})
.indexes([
  {
    name: "IDX_itinerary_day_itinerary_id",
    on: ["itinerary_id"],
    where: "deleted_at IS NULL",
  },
  {
    name: "IDX_itinerary_day_date_unique",
    on: ["itinerary_id", "date"],
    unique: true, // Unique date per itinerary
    where: "deleted_at IS NULL",
  },
  {
    name: "IDX_itinerary_day_sort_order",
    on: ["itinerary_id", "sort_order"],
    where: "deleted_at IS NULL",
  },
]);

/**
 * Itinerary Event Model
 * 
 * Represents individual events within a day.
 * Supports different categories like Flight, Lodging, Activity, etc.
 */
export const ItineraryEvent = model.define("itinerary_event", {
  id: model.id({ prefix: "itin_event" }).primaryKey(),
  day_id: model.text().index(), // Reference to itinerary day
  category: model.enum([
    "Flight",
    "Lodging", 
    "Activity",
    "Cruise",
    "Transport",
    "Info"
  ]),
  type: model.text().nullable(), // e.g., "Departure", "Check-in", "Food/Drink"
  title: model.text(),
  notes: model.text().nullable(),
  start_time: model.text().nullable(), // Time as string (HH:MM)
  end_time: model.text().nullable(),
  duration: model.text().nullable(), // Duration as string
  timezone: model.text().nullable(),
  details: model.json().nullable(), // Flexible schema for category-specific data
  price: model.number().nullable(), // Price in smallest currency unit
  currency: model.text().nullable(),
  media: model.array().nullable(), // Array of image/video URLs
  attachments: model.array().nullable(), // Array of file URLs
  people: model.array().nullable(), // Array of user IDs
})
.indexes([
  {
    name: "IDX_itinerary_event_day_id",
    on: ["day_id"],
    where: "deleted_at IS NULL",
  },
  {
    name: "IDX_itinerary_event_category",
    on: ["category"],
    where: "deleted_at IS NULL",
  },
  {
    name: "IDX_itinerary_event_start_time",
    on: ["day_id", "start_time"],
    where: "deleted_at IS NULL",
  },
]);

export default Itinerary;

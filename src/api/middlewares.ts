import {
  defineMiddlewares,
  validateAndTransformBody,
} from "@camped-ai/framework/http";
import multer from "multer";
import { PostAdminCreateCancellationPolicy } from "./admin/hotel-management/cancellation-policies/route";
import { PostAdminUpdateCancellationPolicy } from "./admin/hotel-management/cancellation-policies/[id]/route";
import { PostAdminSetUserRole } from "./admin/users/[id]/role/route";
import { PostAdminRbacSetup } from "./admin/rbac/setup/route";
import { PostAdminRoleSetup } from "./admin/roles/setup/route";
import { PostAdminCreateRole } from "./admin/roles/route";
import { PutAdminUpdateRole } from "./admin/roles/[id]/route";
import { PostAdminCreateRoleFromTemplate } from "./admin/role-templates/route";
import { PostAdminCreateUser } from "./admin/users/route";
import { PostAdminConfirmPassword } from "./admin/auth/confirm-password/route";
import {
  checkUserStatusOnAuth,
  checkUserStatusOnSession,
} from "./middlewares/auth-status-check";
import {
  PostAdminCreateVendor,
  PostAdminDeleteVendor,
  PostAdminUpdateVendor,
  PostAdminActivateVendor,
} from "./admin/vendor_management/vendors/validators";
import {
  PostAdminCreateSupplier,
  PostAdminUpdateSupplier,
  PostAdminImportSuppliers,
} from "./admin/supplier-management/suppliers/validators";
import {
  PostAdminCreateCategory,
  PostAdminUpdateCategory,
  PostAdminCreateUnitType,
  PostAdminUpdateUnitType,
  PostAdminCreateTag,
  PostAdminUpdateTag,
  PostAdminCreateProductService,
} from "./admin/supplier-management/products-services/validators";
import {
  PostAdminCreateSupplierOffering,
  PostAdminUpdateSupplierOffering,
  PostAdminImportSupplierOfferings,
} from "./admin/supplier-management/supplier-offerings/validators";
import {
  loadUserRole,
  requireAdmin,
  requireAdminOrHotelManager,
  requireAdminOrRoleManagement,
  requirePermission,
} from "./middlewares/rbac";

const upload = multer({ storage: multer.memoryStorage() });

export default defineMiddlewares({
  routes: [
    // Role Setup Route (for initial admin assignment)
    {
      method: ["GET", "POST"],
      matcher: "/admin/roles/setup",
      middlewares: [], // No RBAC protection for initial setup
    },
    {
      method: ["POST"],
      matcher: "/admin/roles/setup",
      middlewares: [validateAndTransformBody(PostAdminRoleSetup)],
    },
    // Legacy RBAC routes for backward compatibility
    {
      method: ["GET", "POST"],
      matcher: "/admin/rbac/setup",
      middlewares: [], // No RBAC protection for initial setup
    },
    {
      method: ["POST"],
      matcher: "/admin/rbac/setup",
      middlewares: [validateAndTransformBody(PostAdminRbacSetup)],
    },

    // Role Management Routes (Admin only)
    {
      method: ["GET", "POST", "DELETE"],
      matcher: "/admin/users/:id/role",
      middlewares: [loadUserRole, requireAdminOrRoleManagement],
    },
    {
      method: ["POST"],
      matcher: "/admin/users/:id/role",
      middlewares: [validateAndTransformBody(PostAdminSetUserRole)],
    },

    // User Creation Routes (Admin only)
    {
      method: ["POST"],
      matcher: "/admin/users",
      middlewares: [loadUserRole, requireAdmin],
    },
    {
      method: ["POST"],
      matcher: "/admin/users",
      middlewares: [validateAndTransformBody(PostAdminCreateUser)],
    },

    // Password Reset Routes - Add user status check
    {
      method: ["GET", "POST"],
      matcher: "/admin/auth/confirm-password",
      middlewares: [loadUserRole],
    },
    {
      method: ["POST"],
      matcher: "/admin/auth/confirm-password",
      middlewares: [validateAndTransformBody(PostAdminConfirmPassword)],
    },

    // User status check for authenticated routes (like /admin/users/me)
    {
      method: ["GET"],
      matcher: "/admin/users/me",
      middlewares: [checkUserStatusOnSession],
    },

    // Session validation routes - check user status early
    {
      method: ["GET", "POST"],
      matcher: "/admin/auth/session",
      middlewares: [checkUserStatusOnSession],
    },

    // Global user status check for all admin routes
    {
      method: ["GET", "POST", "PUT", "DELETE"],
      matcher: "/admin/*",
      middlewares: [checkUserStatusOnAuth, loadUserRole],
    },

    // Global password reset enforcement (DISABLED - users can login but will be redirected to password confirmation)
    // {
    //   method: ["GET", "POST", "PUT", "DELETE"],
    //   matcher: "/admin/*",
    //   middlewares: [enforcePasswordReset],
    // },

    // Custom Role Management Routes (Admin or users with role management permissions)
    {
      method: ["GET", "POST"],
      matcher: "/admin/roles",
      middlewares: [loadUserRole, requireAdminOrRoleManagement],
    },
    {
      method: ["POST"],
      matcher: "/admin/roles",
      middlewares: [validateAndTransformBody(PostAdminCreateRole)],
    },
    {
      method: ["GET"],
      matcher: "/admin/roles/:id",
      middlewares: [loadUserRole], // Allow users to fetch role info (for their own role permissions)
    },
    {
      method: ["PUT", "DELETE"],
      matcher: "/admin/roles/:id",
      middlewares: [loadUserRole, requireAdminOrRoleManagement], // Admin or users with role management permissions
    },
    {
      method: ["PUT"],
      matcher: "/admin/roles/:id",
      middlewares: [validateAndTransformBody(PutAdminUpdateRole)],
    },

    // Permission Management Routes (Admin only)
    {
      method: ["GET"],
      matcher: "/admin/permissions",
      middlewares: [loadUserRole, requireAdmin],
    },

    // Role Template Routes (Admin only)
    {
      method: ["GET", "POST"],
      matcher: "/admin/role-templates",
      middlewares: [loadUserRole, requireAdmin],
    },
    {
      method: ["POST"],
      matcher: "/admin/role-templates",
      middlewares: [validateAndTransformBody(PostAdminCreateRoleFromTemplate)],
    },

    // Hotel Management Routes (Admin or custom role with permissions)
    {
      method: ["GET", "PUT", "DELETE"],
      matcher: "/admin/hotel-management/hotels/:id",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },
    {
      method: ["GET"],
      matcher: "/admin/hotel-management/hotels",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },
    {
      method: ["POST"],
      matcher: "/admin/hotel-management/hotels",
      middlewares: [loadUserRole, requireAdmin], // Only admin can create hotels
    },

    // Existing routes with RBAC protection
    {
      method: ["POST"],
      matcher: "/admin/hotel-management/hotels/:id/upload",
      middlewares: [
        loadUserRole,
        requireAdminOrHotelManager,
        upload.array("files"),
      ],
    },
    {
      method: ["POST"],
      matcher: "/admin/hotel-management/destinations/:id/upload",
      middlewares: [
        loadUserRole,
        requireAdminOrHotelManager,
        upload.array("files"),
      ],
    },
    {
      method: ["POST"],
      matcher: "/admin/hotel-management/cancellation-policies",
      middlewares: [
        loadUserRole,
        requireAdminOrHotelManager,
        validateAndTransformBody(PostAdminCreateCancellationPolicy),
      ],
    },
    {
      method: ["POST"],
      matcher: "/admin/hotel-management/cancellation-policies/:id",
      middlewares: [
        loadUserRole,
        requireAdminOrHotelManager,
        validateAndTransformBody(PostAdminUpdateCancellationPolicy),
      ],
    },
    {
      method: ["POST"],
      matcher: "/admin/hotel-management/room-configs/:id/upload",
      middlewares: [
        loadUserRole,
        requireAdminOrHotelManager,
        upload.array("files"),
      ],
    },
    {
      method: ["POST"],
      matcher: "/admin/add-on-services/:id/upload",
      middlewares: [upload.array("files", 10)],
    },

    // Vendor Management Routes
    {
      method: ["GET"],
      matcher: "/admin/vendor_management/vendors",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },
    {
      method: ["POST"],
      matcher: "/admin/vendor_management/vendors",
      middlewares: [
        loadUserRole,
        requireAdminOrHotelManager,
        validateAndTransformBody(PostAdminCreateVendor),
      ],
    },
    {
      method: ["PUT"],
      matcher: "/admin/vendor_management/vendors",
      middlewares: [
        loadUserRole,
        requireAdminOrHotelManager,
        validateAndTransformBody(PostAdminUpdateVendor),
      ],
    },
    {
      method: ["PATCH"],
      matcher: "/admin/vendor_management/vendors",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },

    // Supplier Management Routes
    {
      method: ["POST"],
      matcher: "/admin/supplier-management/suppliers/:id/documents",
      middlewares: [
        loadUserRole,
        requireAdminOrHotelManager,
        upload.array("files"),
      ],
    },
    {
      method: ["GET", "PUT", "DELETE"],
      matcher: "/admin/supplier-management/documents/:id",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },
    {
      method: ["GET"],
      matcher: "/admin/supplier-management/suppliers/:id/documents",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },
    {
      method: ["DELETE"],
      matcher: "/admin/vendor_management/vendors",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },
    {
      method: ["GET"],
      matcher: "/admin/vendor_management/services",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },
    {
      method: ["GET"],
      matcher: "/admin/vendor_management/products",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },
    {
      method: ["GET"],
      matcher: "/admin/vendor_management/orders",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },

    // Supplier Management Routes (alias for vendor management)
    {
      method: ["GET"],
      matcher: "/admin/supplier-management/suppliers",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },
    {
      method: ["POST"],
      matcher: "/admin/supplier-management/suppliers",
      middlewares: [
        loadUserRole,
        requireAdminOrHotelManager,
        validateAndTransformBody(PostAdminCreateSupplier),
      ],
    },
    {
      method: ["PUT"],
      matcher: "/admin/supplier-management/suppliers",
      middlewares: [
        loadUserRole,
        requireAdminOrHotelManager,
        validateAndTransformBody(PostAdminUpdateSupplier),
      ],
    },
    {
      method: ["PATCH"],
      matcher: "/admin/supplier-management/suppliers",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },
    {
      method: ["DELETE"],
      matcher: "/admin/supplier-management/suppliers",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },
    {
      method: ["GET", "PUT", "DELETE"],
      matcher: "/admin/supplier-management/suppliers/:id",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },
    {
      method: ["PUT"],
      matcher: "/admin/supplier-management/suppliers/:id",
      middlewares: [validateAndTransformBody(PostAdminUpdateSupplier)],
    },

    // Supplier Import Route
    {
      method: ["POST"],
      matcher: "/admin/supplier-management/suppliers/import",
      middlewares: [
        loadUserRole,
        requireAdminOrHotelManager,
        validateAndTransformBody(PostAdminImportSuppliers),
      ],
    },

    // Supplier Products & Services Routes
    {
      method: ["POST"],
      matcher: "/admin/supplier-management/products-services/categories",
      middlewares: [
        loadUserRole,
        requireAdminOrHotelManager,
        validateAndTransformBody(PostAdminCreateCategory),
      ],
    },
    {
      method: ["PUT"],
      matcher: "/admin/supplier-management/products-services/categories/:id",
      middlewares: [
        loadUserRole,
        requireAdminOrHotelManager,
        validateAndTransformBody(PostAdminUpdateCategory),
      ],
    },
    {
      method: ["POST"],
      matcher: "/admin/supplier-management/products-services/unit-types",
      middlewares: [
        loadUserRole,
        requireAdminOrHotelManager,
        validateAndTransformBody(PostAdminCreateUnitType),
      ],
    },
    {
      method: ["PUT"],
      matcher: "/admin/supplier-management/products-services/unit-types/:id",
      middlewares: [
        loadUserRole,
        requireAdminOrHotelManager,
        validateAndTransformBody(PostAdminUpdateUnitType),
      ],
    },
    {
      method: ["POST"],
      matcher: "/admin/supplier-management/products-services/tags",
      middlewares: [
        loadUserRole,
        requireAdminOrHotelManager,
        validateAndTransformBody(PostAdminCreateTag),
      ],
    },
    {
      method: ["PUT"],
      matcher: "/admin/supplier-management/products-services/tags/:id",
      middlewares: [
        loadUserRole,
        requireAdminOrHotelManager,
        validateAndTransformBody(PostAdminUpdateTag),
      ],
    },
    {
      method: ["GET", "DELETE"],
      matcher: "/admin/supplier-management/products-services/categories",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },
    {
      method: ["GET", "DELETE"],
      matcher: "/admin/supplier-management/products-services/categories/:id",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },
    {
      method: ["GET", "DELETE"],
      matcher: "/admin/supplier-management/products-services/unit-types",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },
    {
      method: ["GET", "DELETE"],
      matcher: "/admin/supplier-management/products-services/unit-types/:id",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },
    {
      method: ["GET", "DELETE"],
      matcher: "/admin/supplier-management/products-services/tags",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },
    {
      method: ["GET", "DELETE"],
      matcher: "/admin/supplier-management/products-services/tags/:id",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },

    // Supplier Products & Services Main Routes
    {
      method: ["GET", "POST"],
      matcher: "/admin/supplier-management/products-services",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },
    {
      method: ["POST"],
      matcher: "/admin/supplier-management/products-services",
      middlewares: [validateAndTransformBody(PostAdminCreateProductService)],
    },
    {
      method: ["GET", "PUT", "DELETE"],
      matcher: "/admin/supplier-management/products-services/:id",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },

    // Supplier Products & Services Import Route
    {
      method: ["POST"],
      matcher: "/admin/supplier-management/products-services/import",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },

    // Supplier Offerings Routes
    {
      method: ["GET", "POST"],
      matcher: "/admin/supplier-management/supplier-offerings",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },
    {
      method: ["POST"],
      matcher: "/admin/supplier-management/supplier-offerings",
      middlewares: [validateAndTransformBody(PostAdminCreateSupplierOffering)],
    },
    {
      method: ["GET", "PUT", "DELETE"],
      matcher: "/admin/supplier-management/supplier-offerings/:id",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },
    {
      method: ["PUT"],
      matcher: "/admin/supplier-management/supplier-offerings/:id",
      middlewares: [validateAndTransformBody(PostAdminUpdateSupplierOffering)],
    },

    // Supplier Offerings Import/Export Routes
    {
      method: ["GET"],
      matcher: "/admin/supplier-management/supplier-offerings/template",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },
    {
      method: ["GET"],
      matcher: "/admin/supplier-management/supplier-offerings/export",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },
    {
      method: ["POST"],
      matcher: "/admin/supplier-management/supplier-offerings/import",
      middlewares: [
        loadUserRole,
        requireAdminOrHotelManager,
        validateAndTransformBody(PostAdminImportSupplierOfferings),
      ],
    },

    // Add-ons Inventory Routes
    {
      method: ["GET"],
      matcher: "/admin/inventory/add-ons",
      middlewares: [loadUserRole, requirePermission("addons:view")],
    },
    {
      method: ["GET"],
      matcher: "/admin/inventory/add-ons/:id",
      middlewares: [loadUserRole, requirePermission("addons:view")],
    },
    {
      method: ["PATCH"],
      matcher: "/admin/inventory/add-ons/:id",
      middlewares: [loadUserRole, requirePermission("addons:edit")],
    },

    // Concierge Management Routes
    {
      method: ["GET"],
      matcher: "/admin/concierge-management/itineraries",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },
    {
      method: ["GET", "POST", "PUT", "DELETE"],
      matcher: "/admin/concierge-management/itineraries/:id",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },
    {
      method: ["GET", "POST", "PUT", "DELETE"],
      matcher: "/admin/concierge-management/itineraries/:id/*",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },
    {
      method: ["GET", "POST", "PUT", "DELETE"],
      matcher: "/admin/concierge-management/bookings",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },
    {
      method: ["GET", "POST", "PUT", "DELETE"],
      matcher: "/admin/concierge-management/bookings/:id",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },
    {
      method: ["GET", "POST", "PUT", "DELETE"],
      matcher: "/admin/concierge-management/bookings/:id/*",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },
    {
      method: ["PUT"],
      matcher: "/admin/inventory/add-ons/:id/margin",
      middlewares: [loadUserRole, requirePermission("addons:edit")],
    },
    {
      method: ["PUT", "POST"],
      matcher: "/admin/inventory/add-ons/bulk-margin-update",
      middlewares: [loadUserRole, requirePermission("addons:bulk_update")],
    },
    {
      method: ["POST"],
      matcher: "/admin/inventory/add-ons/sync",
      middlewares: [loadUserRole, requirePermission("addons:edit")],
    },

    // Destination validation endpoint
    {
      method: ["POST"],
      matcher: "/admin/destinations/validate-handles",
      middlewares: [loadUserRole, requireAdminOrHotelManager],
    },

    // Test middleware endpoint
    {
      method: ["POST"],
      matcher: "/admin/test-middleware",
      middlewares: [
        loadUserRole,
        requireAdminOrHotelManager,
        validateAndTransformBody(PostAdminCreateCategory),
      ],
    },
  ],
});

import {
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "src/modules/supplier-products-services";
import { SUPPLIER_MANAGEMENT_MODULE } from "src/modules/vendor_management";
import SupplierProductsServicesModuleService from "src/modules/supplier-products-services/service";
import SupplierManagementModuleService from "src/modules/vendor_management/supplier-service";
import * as ExcelJS from 'exceljs';

/**
 * Resolves hotel and destination IDs to names in custom_fields for supplier offerings
 */
async function resolveCustomFieldNames(
  offering: any,
  scope: any
): Promise<any> {
  if (
    !offering.custom_fields ||
    !offering.product_service?.category?.dynamic_field_schema
  ) {
    return offering;
  }

  const query = scope.resolve("query");
  const resolvedCustomFields = { ...offering.custom_fields };

  // Find hotel, destination, and addon fields in the category schema
  const hotelFields = offering.product_service.category.dynamic_field_schema.filter(
    (field: any) => field.type === "hotels"
  );
  const destinationFields = offering.product_service.category.dynamic_field_schema.filter(
    (field: any) => field.type === "destinations"
  );

  // Resolve hotel names
  for (const field of hotelFields) {
    try {
      const hotelIds = offering.custom_fields[field.key];
      if (Array.isArray(hotelIds) && hotelIds.length > 0) {
        const hotelNames: string[] = [];
        for (const hotelId of hotelIds) {
          try {
            const result = await query.graph({
              entity: "hotel",
              filters: { id: hotelId },
              fields: ["id", "name"],
            });

            if (result.data && result.data.length > 0) {
              hotelNames.push(result.data[0].name);
            } else {
              hotelNames.push(hotelId); // Fallback to ID
            }
          } catch (error) {
            console.warn(
              `Failed to resolve hotel name for ID ${hotelId}:`,
              error
            );
            hotelNames.push(hotelId); // Fallback to ID
          }
        }

        // Store both resolved names and original IDs
        resolvedCustomFields[field.key] = hotelIds;
        resolvedCustomFields[`${field.key}_names`] = hotelNames;
      }
    } catch (error) {
      console.warn(
        `Error resolving hotel names for field ${field.key}:`,
        error
      );
    }
  }

  // Resolve destination names
  for (const field of destinationFields) {
    try {
      const destinationIds = offering.custom_fields[field.key];
      if (Array.isArray(destinationIds) && destinationIds.length > 0) {
        const destinationNames: string[] = [];
        for (const destinationId of destinationIds) {
          try {
            const result = await query.graph({
              entity: "destination",
              filters: { id: destinationId },
              fields: ["id", "name"],
            });

            if (result.data && result.data.length > 0) {
              destinationNames.push(result.data[0].name);
            } else {
              destinationNames.push(destinationId); // Fallback to ID
            }
          } catch (error) {
            console.warn(
              `Failed to resolve destination name for ID ${destinationId}:`,
              error
            );
            destinationNames.push(destinationId); // Fallback to ID
          }
        }

        // Store both resolved names and original IDs
        resolvedCustomFields[field.key] = destinationIds;
        resolvedCustomFields[`${field.key}_names`] = destinationNames;
      }
    } catch (error) {
      console.warn(
        `Error resolving destination names for field ${field.key}:`,
        error
      );
    }
  }

  return {
    ...offering,
    custom_fields: resolvedCustomFields,
  };
}

// Query parameters validation
const GetAdminExportSupplierOfferingsQuery = z.object({
  format: z.enum(["csv", "excel"]).default("excel"),
  supplier_id: z.string().optional(),
  product_service_id: z.string().optional(),
  category_id: z.string().optional(), // Category ID is required for category-specific exports
  status: z.enum(["active", "inactive"]).optional(),
  fields: z.string().optional(), // Comma-separated list of fields to include
});

type GetAdminExportSupplierOfferingsQueryType = z.infer<typeof GetAdminExportSupplierOfferingsQuery>;

/**
 * GET /admin/supplier-management/supplier-offerings/export
 * Export supplier offerings data in CSV or Excel format
 */
export const GET = async (
  req: MedusaRequest<{}, GetAdminExportSupplierOfferingsQueryType>,
  res: MedusaResponse
) => {
  try {
    console.log("🌐 GET /admin/supplier-management/supplier-offerings/export called");
    console.log("📥 Query params:", req.query);

    const supplierProductsServicesService: SupplierProductsServicesModuleService = 
      req.scope.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);
    const supplierManagementService: SupplierManagementModuleService = 
      req.scope.resolve(SUPPLIER_MANAGEMENT_MODULE);

    // Parse and validate query parameters
    const { format = "excel", fields, ...filters } = req.query;

    // Parse selected fields if provided
    const selectedFields = fields && typeof fields === 'string' ?
      fields.split(',').map((f: string) => f.trim()).filter(Boolean) : null;

    console.log("🔍 Export Debug - Selected fields:", selectedFields);

    // Build filters for supplier offerings
    const offeringFilters: any = {};
    if (filters.supplier_id) offeringFilters.supplier_id = filters.supplier_id;
    if (filters.product_service_id) offeringFilters.product_service_id = filters.product_service_id;
    if (filters.category_id) offeringFilters.category_id = filters.category_id;
    if (filters.status) offeringFilters.status = filters.status;

    // Fetch supplier offerings with filters
    const offeringsResult = await supplierProductsServicesService.listSupplierOfferingsWithFilters(
      offeringFilters,
      { limit: 10000 } // Large limit to get all data for export
    );

    const offerings = offeringsResult.data || [];

    if (offerings.length === 0) {
      return res.status(404).json({
        type: "no_data",
        message: "No supplier offerings found matching the specified criteria",
      });
    }

    // Resolve custom field names for all offerings
    const resolvedOfferings = await Promise.all(
      offerings.map(async (offering: any) => {
        try {
          const resolved = await resolveCustomFieldNames(offering, req.scope);
          console.log(`🔍 Name Resolution Debug - Offering ${offering.id}:`, {
            original_custom_fields: offering.custom_fields,
            resolved_custom_fields: resolved.custom_fields,
            has_to_resorts_names: !!resolved.custom_fields?.to_resorts_names,
            to_resorts_names_value: resolved.custom_fields?.to_resorts_names
          });
          return resolved;
        } catch (error) {
          console.warn(
            `Failed to resolve names for offering ${offering.id}:`,
            error
          );
          return offering; // Return original offering if name resolution fails
        }
      })
    );

    // Fetch data for export and dropdown generation
    const [allSuppliersResult, allProductsServicesResult, categoriesResult] = await Promise.all([
      // Fetch all suppliers for dropdown generation
      supplierManagementService.listSuppliers({}, { skip: 0, take: 100 }),
      // Fetch products/services filtered by category if specified
      filters.category_id
        ? supplierProductsServicesService.listProductServicesWithFiltersAndCount({ category_id: filters.category_id as string }, { skip: 0, take: 100 })
        : supplierProductsServicesService.listProductServicesWithFiltersAndCount({}, { skip: 0, take: 100 }),
      // Fetch all categories for dropdown generation
      supplierProductsServicesService.listCategories({}, { skip: 0, take: 100 })
    ]);

    // Extract data for export and dropdown generation
    const allSuppliers = (allSuppliersResult as any).suppliers || (allSuppliersResult as any).data || [];
    const allProductsServices = (allProductsServicesResult as any).data || allProductsServicesResult || [];
    const categories = (categoriesResult as any).data || categoriesResult || [];

    // Get category schema for custom field processing
    let categorySchema: any[] = [];
    let lockedFields: any[] = [];

    if (filters.category_id && categories.length > 0) {
      const category = categories.find((c: any) => c.id === filters.category_id);
      if (category && category.dynamic_field_schema) {
        console.log("🔍 Export Debug - Full category schema:", JSON.stringify(category.dynamic_field_schema, null, 2));

        const allFields = category.dynamic_field_schema.filter((field: any) =>
          field.used_in_supplier_offering !== false // Show fields unless explicitly marked as false
        );

        console.log("🔍 Export Debug - Filtered fields for supplier offerings:", allFields.map((f: any) => ({ key: f.key, used_in_supplier_offering: f.used_in_supplier_offering })));

        // Separate locked fields for special handling
        lockedFields = allFields.filter((field: any) => field.locked_in_offerings === true);
        categorySchema = allFields; // Keep all fields for processing
      }
    }

    // Define all available base columns
    const allBaseColumns = [
      'supplier_name',
      'product_service_name',
      'category_name',
      'commission',
      'gross_price',
      'net_cost',
      'margin_rate',
      'selling_price',
      'exchange_rate',
      'selling_price_selling_currency',
      'currency',
      'status',
      'active_from',
      'active_to',
      'availability_notes'
    ];

    // Filter base columns based on selected fields (if provided)
    const baseColumns = selectedFields ?
      allBaseColumns.filter(col => selectedFields.includes(col)) :
      allBaseColumns;

    // Add dynamic field columns for export (include all fields, locked or not)
    const dynamicColumns: string[] = [];
    categorySchema.forEach((field: any) => {
      const fieldKey = field.key || field.field_name;
      if (field.type === 'number-range') {
        // For number-range fields, create separate from and to columns
        const fromField = `${fieldKey}_from`;
        const toField = `${fieldKey}_to`;

        // Only add if selected (or if no field selection is specified)
        if (!selectedFields || selectedFields.includes(fromField)) {
          dynamicColumns.push(fromField);
        }
        if (!selectedFields || selectedFields.includes(toField)) {
          dynamicColumns.push(toField);
        }
      } else {
        // Only add if selected (or if no field selection is specified)
        if (!selectedFields || selectedFields.includes(fieldKey)) {
          dynamicColumns.push(fieldKey);
        }
      }
    });

    const allColumns = [...baseColumns, ...dynamicColumns];
    // Transform data for export with human-readable names
    const exportData = resolvedOfferings.map(offering => {
      // Use allSuppliers and allProductsServices for proper name lookup
      const supplier = allSuppliers.find((s: any) => s.id === offering.supplier_id);
      const productService = allProductsServices.find((ps: any) => ps.id === offering.product_service_id);



      // Define all possible field values
      const allFieldValues: any = {
        supplier_name: supplier?.name || offering.supplier_id,
        product_service_name: productService?.name || offering.product_service_id,
        category_name: productService?.category?.name || '',
        commission: offering.commission ? (parseFloat(String(offering.commission)) * 100).toFixed(1) + '%' : '',
        gross_price: offering.gross_price ? parseFloat(String(offering.gross_price)).toFixed(2) : '',
        net_cost: (offering.net_cost || offering.net_price || offering.cost) ?
          parseFloat(String(offering.net_cost || offering.net_price || offering.cost)).toFixed(2) : '',
        margin_rate: offering.margin_rate ? (parseFloat(String(offering.margin_rate)) * 100).toFixed(1) + '%' : '',
        selling_price: offering.selling_price ? parseFloat(String(offering.selling_price)).toFixed(2) : '',
        exchange_rate: offering.exchange_rate ? parseFloat(String(offering.exchange_rate)).toFixed(4) : '',
        selling_price_selling_currency: offering.selling_currency ?
          offering.selling_currency : '',
        currency: supplier?.default_currency || offering.currency || '',
        status: offering.status || 'active',
        active_from: (() => {
          if (!offering.active_from) return '';
          // Convert to local date to match UI display
          const originalDate = new Date(offering.active_from);
          const localDateString = originalDate.toLocaleDateString('en-CA'); // YYYY-MM-DD format
          return localDateString;
        })(),
        active_to: (() => {
          if (!offering.active_to) return '';
          // Convert to local date to match UI display
          const originalDate = new Date(offering.active_to);
          const localDateString = originalDate.toLocaleDateString('en-CA'); // YYYY-MM-DD format
          return localDateString;
        })(),
        availability_notes: offering.availability_notes || '',
      };

      // Create export record with only selected fields
      const exportRecord: any = {};
      baseColumns.forEach(field => {
        if (allFieldValues.hasOwnProperty(field)) {
          exportRecord[field] = allFieldValues[field];
        }
      });

      // Add custom fields with proper formatting (include all fields for export)
      if (offering.custom_fields && typeof offering.custom_fields === 'object') {
        categorySchema.forEach((field: any) => {
          const fieldKey = field.key || field.field_name;
          const value = offering.custom_fields[fieldKey];

          if (field.type === 'number-range' && value) {
            // Handle number-range fields - split into from/to columns
            const fromField = `${fieldKey}_from`;
            const toField = `${fieldKey}_to`;

            if (typeof value === 'string' && value.includes('-')) {
              const [from, to] = value.split('-').map(v => v.trim());
              if (!selectedFields || selectedFields.includes(fromField)) {
                exportRecord[fromField] = from || '';
              }
              if (!selectedFields || selectedFields.includes(toField)) {
                exportRecord[toField] = to || '';
              }
            } else if (typeof value === 'object' && value.min !== undefined && value.max !== undefined) {
              if (!selectedFields || selectedFields.includes(fromField)) {
                exportRecord[fromField] = value.min || '';
              }
              if (!selectedFields || selectedFields.includes(toField)) {
                exportRecord[toField] = value.max || '';
              }
            } else {
              // Handle single value or unexpected format
              if (!selectedFields || selectedFields.includes(fromField)) {
                exportRecord[fromField] = value || '';
              }
              if (!selectedFields || selectedFields.includes(toField)) {
                exportRecord[toField] = '';
              }
            }
          } else if (field.type === 'multi-select' && Array.isArray(value)) {
            // Handle multi-select fields as comma-separated values
            if (!selectedFields || selectedFields.includes(fieldKey)) {
              exportRecord[fieldKey] = value.join(',');
            }
          } else if (field.type === 'hotels' && Array.isArray(value)) {
            // Use resolved hotel names if available, otherwise use IDs
            if (!selectedFields || selectedFields.includes(fieldKey)) {
              const namesField = `${fieldKey}_names`;
              const resolvedNames = offering.custom_fields[namesField];
              if (resolvedNames && Array.isArray(resolvedNames)) {
                exportRecord[fieldKey] = resolvedNames.join(', ');
              } else {
                exportRecord[fieldKey] = value.join(', ');
              }
            }
          } else if (field.type === 'destinations' && Array.isArray(value)) {
            // Use resolved destination names if available, otherwise use IDs
            if (!selectedFields || selectedFields.includes(fieldKey)) {
              const namesField = `${fieldKey}_names`;
              const resolvedNames = offering.custom_fields[namesField];
              if (resolvedNames && Array.isArray(resolvedNames)) {
                exportRecord[fieldKey] = resolvedNames.join(', ');
                console.log(`🔍 Using resolved names: ${resolvedNames.join(', ')}`);
              } else {
                exportRecord[fieldKey] = value.join(', ');
                console.log(`🔍 Using original IDs: ${value.join(', ')}`);
              }
            }
          } else if (field.type === 'dropdown' && field.options) {
            // Handle dropdown fields - resolve to human-readable labels
            if (!selectedFields || selectedFields.includes(fieldKey)) {
              if (Array.isArray(field.options)) {
                const option = field.options.find((opt: any) =>
                  (typeof opt === 'object' ? opt.value : opt) === value
                );
                if (option) {
                  exportRecord[fieldKey] = typeof option === 'object' ? option.label : option;
                } else {
                  exportRecord[fieldKey] = value || '';
                }
              } else {
                exportRecord[fieldKey] = value || '';
              }
            }
          } else {
            // Handle all other field types
            if (!selectedFields || selectedFields.includes(fieldKey)) {
              exportRecord[fieldKey] = value || '';
            }
          }
        });
      }

      // Ensure all selected columns are present in the data
      allColumns.forEach(col => {
        if (!(col in exportRecord)) {
          exportRecord[col] = '';
        }
      });

      return exportRecord;
    });

    const timestamp = new Date().toISOString().split('T')[0];

    if (format === "csv") {
      // Generate CSV with selected columns
      const csvContent = convertToCSV(exportData, allColumns);
      const filename = `supplier_offerings_export_${timestamp}.csv`;

      res.setHeader('Content-Type', 'text/csv;charset=utf-8');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Content-Length', Buffer.byteLength(csvContent, 'utf8'));

      console.log(`📤 Exported ${exportData.length} supplier offerings as CSV`);
      return res.send(csvContent);
    } else {
      // Generate Excel with ExcelJS (with dynamic dropdowns)
      const workbook = new ExcelJS.Workbook();

      // Get category name for filename if category_id is provided
      let categoryName = "";
      if (filters.category_id && categories.length > 0) {
        const category = categories.find((c: any) => c.id === filters.category_id);
        if (category) {
          categoryName = `_${category.name.replace(/[^a-zA-Z0-9]/g, '_')}`;
        }
      }

      // Create main data worksheet
      const worksheetName = filters.category_id && categoryName
        ? `${categoryName.substring(1, 21)}_Offerings`.replace(/[^a-zA-Z0-9_]/g, '_')
        : 'Supplier_Offerings';

      const mainWorksheet = workbook.addWorksheet(worksheetName);

      // Add headers with formatting - use allColumns to ensure all selected fields are included
      const headers = allColumns;
      const headerRow = mainWorksheet.addRow(headers);
      headerRow.font = { bold: true, color: { argb: "FFFFFF" } };
      headerRow.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "4472C4" },
      };

      // Add data rows
      exportData.forEach(row => {
        const values = headers.map(header => row[header] || '');
        mainWorksheet.addRow(values);
      });

      // Set column widths
      headers.forEach((header, index) => {
        mainWorksheet.getColumn(index + 1).width = Math.max(header.length, 20);
      });

      // Apply data validation to create dropdowns for editable fields
      const supplierNameColumnIndex = headers.indexOf('supplier_name') + 1;
      const productServiceNameColumnIndex = headers.indexOf('product_service_name') + 1;
      const statusColumnIndex = headers.indexOf('status') + 1;
      // Removed currencyColumnIndex - currency column no longer exists

      // Apply validation to data rows (starting from row 2, up to row with data + 50 extra rows for new entries)
      const maxRow = Math.max(exportData.length + 50, 100);

      // Supplier name dropdown validation
      if (supplierNameColumnIndex > 0) {
        for (let rowNum = 2; rowNum <= maxRow; rowNum++) {
          const cell = mainWorksheet.getCell(rowNum, supplierNameColumnIndex);
          cell.dataValidation = {
            type: 'list',
            allowBlank: true,
            formulae: [`ValidationData!$A$2:$A$${allSuppliers.length + 1}`]
          };
        }
      }

      // Product/service name dropdown validation (category-specific)
      if (productServiceNameColumnIndex > 0) {
        const categoryProducts = allProductsServices.filter((ps: any) =>
          !filters.category_id || ps.category_id === filters.category_id
        );
        for (let rowNum = 2; rowNum <= maxRow; rowNum++) {
          const cell = mainWorksheet.getCell(rowNum, productServiceNameColumnIndex);
          cell.dataValidation = {
            type: 'list',
            allowBlank: true,
            formulae: [`ValidationData!$B$2:$B$${categoryProducts.length + 1}`]
          };
        }
      }

      // Status dropdown validation
      if (statusColumnIndex > 0) {
        for (let rowNum = 2; rowNum <= maxRow; rowNum++) {
          const cell = mainWorksheet.getCell(rowNum, statusColumnIndex);
          cell.dataValidation = {
            type: 'list',
            allowBlank: true,
            formulae: [`ValidationData!$C$2:$C$3`] // active, inactive
          };
        }
      }

      // Removed currency dropdown validation - currency column no longer exists

      // Add validation for custom field dropdowns
      let validationColumn = 5; // Start from column E
      categorySchema.forEach((field: any) => {
        const fieldKey = field.key || field.field_name;

        if (field.type === 'dropdown' && field.options && field.options.length > 0) {
          const fieldColumnIndex = headers.indexOf(fieldKey) + 1;
          if (fieldColumnIndex > 0) {
            const columnLetter = String.fromCharCode(64 + validationColumn);
            for (let rowNum = 2; rowNum <= maxRow; rowNum++) {
              const cell = mainWorksheet.getCell(rowNum, fieldColumnIndex);
              cell.dataValidation = {
                type: 'list',
                allowBlank: true,
                formulae: [`ValidationData!$${columnLetter}$2:$${columnLetter}$${field.options.length + 1}`]
              };
            }
            validationColumn++;
          }
        }
      });

      // Add validation data for dropdowns
      if (filters.category_id) {
        // Create validation data sheet
        const validationSheet = workbook.addWorksheet('ValidationData');
        validationSheet.state = 'hidden'; // Hide this sheet

        // Add supplier names for dropdown
        validationSheet.getCell('A1').value = 'Suppliers';
        validationSheet.getCell('A1').font = { bold: true };
        allSuppliers.forEach((supplier: any, index: number) => {
          validationSheet.getCell(`A${index + 2}`).value = supplier.name;
        });

        // Add product/service names for dropdown (filtered by category)
        validationSheet.getCell('B1').value = 'Products/Services';
        validationSheet.getCell('B1').font = { bold: true };
        const categoryProducts = allProductsServices.filter((ps: any) => ps.category_id === filters.category_id);
        categoryProducts.forEach((ps: any, index: number) => {
          validationSheet.getCell(`B${index + 2}`).value = ps.name;
        });

        // Add status options
        validationSheet.getCell('C1').value = 'Status';
        validationSheet.getCell('C1').font = { bold: true };
        ['active', 'inactive'].forEach((status, index) => {
          validationSheet.getCell(`C${index + 2}`).value = status;
        });

        // Removed currency options - currency column no longer exists

        // Add custom field dropdown options
        let currentColumn = 4; // Start from column D (after A-C)
        categorySchema.forEach((field: any) => {
          const fieldKey = field.key || field.field_name;

          if (field.type === 'dropdown' && field.options && field.options.length > 0) {
            const columnLetter = String.fromCharCode(64 + currentColumn); // Convert to letter (E, F, G, etc.)
            validationSheet.getCell(`${columnLetter}1`).value = fieldKey;
            validationSheet.getCell(`${columnLetter}1`).font = { bold: true };

            field.options.forEach((option: any, index: number) => {
              const optionValue = typeof option === 'string' ? option : option.value || option.label;
              validationSheet.getCell(`${columnLetter}${index + 2}`).value = optionValue;
            });

            validationSheet.getColumn(currentColumn).width = 20;
            currentColumn++;
          }
        });

        // Set column widths for validation sheet
        validationSheet.getColumn('A').width = 30;
        validationSheet.getColumn('B').width = 30;
        validationSheet.getColumn('C').width = 15;
        // Removed column D width setting - currency column no longer exists
      }

      // Create reference sheets for multi-select fields
      const multiSelectFields = new Map();
      categorySchema.forEach((field: any) => {
        const fieldKey = field.key || field.field_name;

        if (field.type === 'multi-select' && field.options && field.options.length > 0) {
          multiSelectFields.set(`${fieldKey}_options`, field.options);
        }
      });

      // Create reference sheets for multi-select fields
      multiSelectFields.forEach((options, fieldKey) => {
        if (options.length > 0) {
          // Ensure sheet name doesn't exceed Excel's 31 character limit
          const sheetName = `${fieldKey.replace(/_/g, '_').substring(0, 27)}_Ref`;
          const referenceSheet = workbook.addWorksheet(sheetName);

          const headers = ['name', 'description'];
          const headerRow = referenceSheet.addRow(headers);
          headerRow.font = { bold: true, color: { argb: "FFFFFF" } };
          headerRow.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: { argb: "4472C4" },
          };

          // Add options data
          options.forEach((option: any) => {
            const optionValue = typeof option === 'string' ? option : option.value || option.label;
            referenceSheet.addRow([optionValue, '']);
          });

          // Set column widths
          referenceSheet.getColumn(1).width = 25;
          referenceSheet.getColumn(2).width = 40;
        }
      });

      // Add instructions sheet
      const instructionsSheet = workbook.addWorksheet('Instructions');

      // Add instructions header
      const instructionsHeader = instructionsSheet.addRow(['Field', 'Description', 'Required', 'Valid Values']);
      instructionsHeader.font = { bold: true, color: { argb: "FFFFFF" } };
      instructionsHeader.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "4472C4" },
      };

      // Add instructions for each field
      const instructionsData = [
        {
          Field: 'supplier_name',
          Description: 'Name of the supplier offering the product/service',
          Required: 'Yes',
          'Valid Values': 'Select from dropdown list of available suppliers'
        },
        {
          Field: 'product_service_name',
          Description: filters.category_id
            ? `Name of the product or service being offered (filtered for ${categories.find((c: any) => c.id === filters.category_id)?.name || 'selected'} category)`
            : 'Name of the product or service being offered',
          Required: 'Yes',
          'Valid Values': 'Select from dropdown list of available products/services'
        },
        {
          Field: 'category_name',
          Description: 'Category of the product/service',
          Required: 'No',
          'Valid Values': 'Auto-populated from product/service'
        },
        {
          Field: 'commission',
          Description: 'Commission percentage',
          Required: 'No',
          'Valid Values': 'Percentage value (e.g., 10.5%)'
        },
        {
          Field: 'gross_price',
          Description: 'Gross price with currency',
          Required: 'No',
          'Valid Values': 'Decimal number with currency (e.g., 100.00 CHF)'
        },
        {
          Field: 'net_cost',
          Description: 'Net cost with currency',
          Required: 'No',
          'Valid Values': 'Decimal number with currency (e.g., 90.00 CHF)'
        },
        {
          Field: 'margin_rate',
          Description: 'Margin rate percentage',
          Required: 'No',
          'Valid Values': 'Percentage value (e.g., 11.1%)'
        },
        {
          Field: 'selling_price',
          Description: 'Selling price in cost currency',
          Required: 'No',
          'Valid Values': 'Decimal number with currency (e.g., 110.00 CHF)'
        },
        {
          Field: 'exchange_rate',
          Description: 'Exchange rate for currency conversion',
          Required: 'No',
          'Valid Values': 'Decimal number (e.g., 1.0850)'
        },
        {
          Field: 'selling_price_selling_currency',
          Description: 'Selling price in selling currency',
          Required: 'No',
          'Valid Values': 'Decimal number with currency (e.g., 119.35 EUR)'
        },
        {
          Field: 'status',
          Description: 'Status of the offering',
          Required: 'No',
          'Valid Values': 'active, inactive'
        },
        {
          Field: 'active_from',
          Description: 'Start date when this offering becomes active',
          Required: 'No',
          'Valid Values': 'Date in YYYY-MM-DD format'
        },
        {
          Field: 'active_to',
          Description: 'End date when this offering expires',
          Required: 'No',
          'Valid Values': 'Date in YYYY-MM-DD format'
        },
        {
          Field: 'cost',
          Description: 'Cost of the offering (currency is auto-populated from supplier default currency)',
          Required: 'Yes',
          'Valid Values': 'Numeric value (e.g., 250.00)'
        },
        // Removed currency field - it's auto-populated from supplier default currency
        {
          Field: 'availability_notes',
          Description: 'Additional notes about availability or restrictions',
          Required: 'No',
          'Valid Values': 'Free text'
        },
        {
          Field: 'status',
          Description: 'Status of the offering',
          Required: 'Yes',
          'Valid Values': 'Select from dropdown: active, inactive'
        }
      ];

      // Add custom field instructions
      if (categorySchema.length > 0 && filters.category_id) {
        const category = categories.find((c: any) => c.id === filters.category_id);

        // Add separator for custom fields
        instructionsData.push({
          Field: '--- CUSTOM FIELDS ---',
          Description: `Category-specific fields for ${category?.name || 'selected category'}`,
          Required: '',
          'Valid Values': ''
        });

        // Add note about locked fields if any exist
        if (lockedFields.length > 0) {
          instructionsData.push({
            Field: '--- LOCKED FIELDS ---',
            Description: 'The following fields are locked and values are inherited from products/services. These fields are shown in a separate sheet for reference only and should not be edited.',
            Required: '',
            'Valid Values': ''
          });
        }

        // Add instructions for each custom field
        categorySchema.forEach((field: any) => {
          const fieldKey = field.key || field.field_name;
          let validValues = 'Any text';
          let description = field.label || fieldKey;

          // Add note about locked status
          if (field.locked_in_offerings) {
            description += ' (LOCKED - inherited from product/service)';
          }

          if (field.type === 'dropdown' && field.options) {
            validValues = `${field.options.map((o: any) => typeof o === 'string' ? o : o.label || o.value).join(', ')} (dropdown available)`;
          } else if (field.type === 'multi-select' && field.options) {
            validValues = `Comma-separated from: ${field.options.map((o: any) => typeof o === 'string' ? o : o.label || o.value).join(', ')} (no dropdown - refer to ${fieldKey}_options_Ref sheet)`;
          } else if (field.type === 'boolean') {
            validValues = 'true, false';
          } else if (field.type === 'number') {
            validValues = 'Number';
          } else if (field.type === 'date') {
            validValues = 'YYYY-MM-DD format';
          } else if (field.type === 'number-range') {
            // For number-range fields, add instructions for both from and to columns
            instructionsData.push({
              Field: `${fieldKey}_from`,
              Description: `Minimum value for ${description}`,
              Required: field.required ? 'Yes' : 'No',
              'Valid Values': 'Number'
            });

            instructionsData.push({
              Field: `${fieldKey}_to`,
              Description: `Maximum value for ${description}`,
              Required: field.required ? 'Yes' : 'No',
              'Valid Values': 'Number'
            });

            // Skip adding the base field since we've added the _from and _to versions
            return;
          } else if (field.type === 'hotels') {
            validValues = 'Comma-separated hotel names';
          } else if (field.type === 'destinations') {
            validValues = 'Comma-separated destination names';
          }

          instructionsData.push({
            Field: fieldKey,
            Description: description,
            Required: field.required ? 'Yes' : 'No',
            'Valid Values': validValues
          });
        });
      }

      // Add instruction rows
      instructionsData.forEach((instruction: any) => {
        instructionsSheet.addRow([
          instruction.Field,
          instruction.Description,
          instruction.Required,
          instruction['Valid Values']
        ]);
      });

      // Set column widths for instructions sheet
      instructionsSheet.getColumn(1).width = 25;
      instructionsSheet.getColumn(2).width = 40;
      instructionsSheet.getColumn(3).width = 10;
      instructionsSheet.getColumn(4).width = 50;

      // Create locked fields reference sheet if there are locked fields
      if (lockedFields.length > 0) {
        const lockedFieldsSheet = workbook.addWorksheet('Locked_Fields_Reference');

        // Add header with styling
        const lockedHeaderRow = lockedFieldsSheet.addRow([
          'Field Name',
          'Current Value',
          'Source',
          'Note'
        ]);
        lockedHeaderRow.font = { bold: true, color: { argb: "FFFFFF" } };
        lockedHeaderRow.fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "DC3545" }, // Red background to indicate locked status
        };

        // Add warning note
        const warningRow = lockedFieldsSheet.addRow([
          'WARNING',
          'These fields are locked and inherited from products/services',
          'DO NOT EDIT',
          'Values shown are for reference only'
        ]);
        warningRow.font = { bold: true, color: { argb: "DC3545" } };
        warningRow.fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "FFF3CD" }, // Light yellow background
        };

        // Add locked field data for each offering with inherited values
        exportData.forEach((offering: any, index: number) => {
          // Find the corresponding original offering to get product/service details
          const originalOffering = offerings.find((o: any) =>
            (allSuppliers.find((s: any) => s.id === o.supplier_id)?.name === offering.supplier_name) &&
            (allProductsServices.find((ps: any) => ps.id === o.product_service_id)?.name === offering.product_service_name)
          );

          // Find the product/service to get its custom fields
          const productService = allProductsServices.find((ps: any) => ps.id === originalOffering?.product_service_id);

          lockedFields.forEach((field: any) => {
            const fieldKey = field.key || field.field_name;

            // Get inherited value from product/service custom fields
            let inheritedValue = 'Not set';
            if (productService?.custom_fields && productService.custom_fields[fieldKey] !== undefined) {
              const rawValue = productService.custom_fields[fieldKey];

              // Format the value based on field type
              if (field.type === 'number-range' && typeof rawValue === 'object' && rawValue !== null) {
                if (rawValue.min !== undefined && rawValue.max !== undefined) {
                  inheritedValue = `${rawValue.min} - ${rawValue.max}`;
                } else if (rawValue.min !== undefined) {
                  inheritedValue = `${rawValue.min}+`;
                } else if (rawValue.max !== undefined) {
                  inheritedValue = `Up to ${rawValue.max}`;
                }
              } else if (field.type === 'multi-select' && Array.isArray(rawValue)) {
                inheritedValue = rawValue.join(', ');
              } else if (field.type === 'boolean') {
                inheritedValue = rawValue ? 'Yes' : 'No';
              } else if (field.type === 'date' && rawValue) {
                try {
                  inheritedValue = new Date(rawValue).toLocaleDateString('de-CH');
                } catch {
                  inheritedValue = rawValue.toString();
                }
              } else if (rawValue !== null && rawValue !== undefined) {
                inheritedValue = rawValue.toString();
              }
            }

            lockedFieldsSheet.addRow([
              field.label || fieldKey,
              inheritedValue,
              `Product/Service: ${offering.product_service_name}`,
              `Row ${index + 1} in main sheet`
            ]);
          });
        });

        // Set column widths for locked fields sheet
        lockedFieldsSheet.getColumn(1).width = 25;
        lockedFieldsSheet.getColumn(2).width = 30;
        lockedFieldsSheet.getColumn(3).width = 35;
        lockedFieldsSheet.getColumn(4).width = 25;
      }

      // Generate filename
      const filename = `supplier_offerings_export${categoryName}_${timestamp}.xlsx`;

      // Set response headers
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

      // Write the workbook to the response
      await workbook.xlsx.write(res);

      console.log(`📤 Exported ${exportData.length} supplier offerings as Excel with dynamic dropdowns`);
    }
  } catch (error) {
    console.error("💥 Export supplier offerings error:", error);
    
    return res.status(500).json({
      type: "server_error",
      message: error instanceof Error ? error.message : "Failed to export supplier offerings",
    });
  }
};

// CSV conversion utility
const convertToCSV = (data: any[], columns?: string[]): string => {
  if (!data || data.length === 0) return '';

  // Use provided columns or fall back to keys from first data row
  const headers = columns || Object.keys(data[0]);
  const csvRows = [
    headers.join(','),
    ...data.map(row =>
      headers.map(header => {
        const value = row[header];
        // Handle values that might contain commas or quotes
        if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value ?? '';
      }).join(',')
    )
  ];

  return csvRows.join('\n');
};

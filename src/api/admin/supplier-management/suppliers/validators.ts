import { z } from "zod";

// Supplier creation validator
export const PostAdminCreateSupplier = z.object({
  name: z.string().min(1, "Name is required"),
  handle: z.string().optional(),
  description: z.string().optional(),

  // Supplier Type (Company vs Individual)
  supplier_type: z.enum(["Company", "Individual"]).default("Company"),


  preference: z.enum(["Preferred", "Backup"]).optional(),
  status: z
    .enum(["Active", "Inactive", "Pending Approval", "Suspended", "Terminated"])
    .default("Active"),
  verification_status: z
    .enum(["verified", "unverified", "in_review"])
    .default("unverified"),

  // Business & Region Information
  region: z.string().optional(),
  timezone: z.string().optional(),
  language_preference: z.array(z.string()).optional(),

  // Financial Information
  payment_method: z.string().optional(),
  payout_terms: z.string().optional(),
  bank_account_details: z.string().optional(),

  // Business Details
  business_registration_number: z.string().optional(),
  tax_id: z.string().optional(),
  website: z.string().url().optional().or(z.literal("")),

  // Default Currency
  default_currency: z
    .string()
    .regex(/^[A-Z]{3}$/, "Currency must be a valid 3-letter code")
    .optional(),

  // Address Information
  address: z.string().optional(),

  // Categories
  categories: z.array(z.string()).optional(),

  // Supplier Contacts
  contacts: z
    .array(
      z.object({
        name: z.string().min(1, "Contact name is required"),
        email: z.string().email("Valid email is required"),
        phone_number: z
          .union([z.string(), z.number()])
          .transform((val) => (val ? String(val) : undefined))
          .optional(),
        is_whatsapp: z.boolean().default(false),
        is_primary: z.boolean().default(false),
      })
    )
    .optional(),

  metadata: z.record(z.any()).optional(),
});

// Supplier update validator
export const PostAdminUpdateSupplier = z.object({
  id: z.string().optional(), // For bulk updates
  name: z.string().min(1).optional(),
  handle: z.string().optional(),
  description: z.string().optional(),

  // Supplier Type (Company vs Individual)
  supplier_type: z.enum(["Company", "Individual"]).optional(),


  preference: z.enum(["Preferred", "Backup"]).optional(),
  status: z
    .enum(["Active", "Inactive", "Pending Approval", "Suspended", "Terminated"])
    .optional(),
  verification_status: z
    .enum(["verified", "unverified", "in_review"])
    .optional(),

  // Business & Region Information
  region: z.string().optional(),
  timezone: z.string().optional(),
  language_preference: z.array(z.string()).optional(),

  // Financial Information
  payment_method: z.string().optional(),
  payout_terms: z.string().optional(),
  bank_account_details: z.string().optional(),

  // Business Details
  business_registration_number: z.string().optional(),
  tax_id: z.string().optional(),
  website: z.string().url().optional().or(z.literal("")),

  // Address Information
  address: z.string().optional(),

  // Default Currency
  default_currency: z
    .string()
    .regex(/^[A-Z]{3}$/, "Currency must be a valid 3-letter code")
    .optional(),

  // Categories
  categories: z.array(z.string()).optional(),

  // Supplier Contacts
  contacts: z
    .array(
      z.object({
        id: z.string().optional(), // For updating existing contacts
        name: z.string().min(1, "Contact name is required"),
        email: z.string().email("Valid email is required"),
        phone_number: z.string().optional(),
        is_whatsapp: z.boolean().default(false),
        is_primary: z.boolean().default(false),
      })
    )
    .optional(),

  metadata: z.record(z.any()).optional(),
});

// Supplier deletion validator
export const PostAdminDeleteSupplier = z.object({
  id: z.string().min(1, "Supplier ID is required"),
});

// Bulk import suppliers validator
export const PostAdminImportSuppliers = z.object({
  suppliers: z
    .array(
      z.object({
        name: z.string().min(1, "Name is required"),
        handle: z.string().optional(),
        description: z.string().optional(),

        // Supplier Type (Company vs Individual)
        supplier_type: z.enum(["Company", "Individual"]).default("Company"),


        preference: z.enum(["Preferred", "Backup"]).optional(),

        // Status fields with transformation for empty strings
        status: z
          .union([
            z.enum([
              "Active",
              "Inactive",
              "Pending Approval",
              "Suspended",
              "Terminated",
            ]),
            z.literal("").transform(() => "Active" as const),
            z.null().transform(() => "Active" as const),
            z.undefined().transform(() => "Active" as const),
          ])
          .optional()
          .default("Active"),

        verification_status: z
          .union([
            z.enum(["verified", "unverified", "in_review"]),
            z.literal("").transform(() => "unverified" as const),
            z.null().transform(() => "unverified" as const),
            z.undefined().transform(() => "unverified" as const),
          ])
          .optional()
          .default("unverified"),

        // Business & Region Information
        region: z.string().optional(),
        timezone: z.string().optional(),
        language_preference: z
          .union([
            z.array(z.string()),
            z
              .string()
              .transform((val) =>
                val ? val.split(",").map((s) => s.trim()) : []
              ),
            z.null().transform(() => []),
            z.undefined().transform(() => []),
          ])
          .optional(),

        // Financial Information
        payment_method: z.string().optional(),
        payout_terms: z.string().optional(),
        bank_account_details: z.string().optional(),

        // Business Details
        business_registration_number: z.string().optional(),
        tax_id: z.string().optional(),
        website: z.string().url().optional().or(z.literal("")),

        // Address Information
        address: z.string().optional(),

        // Default Currency with transformation for empty strings and currency descriptions
        default_currency: z
          .union([
            z
              .string()
              .regex(/^[A-Z]{3}$/, "Currency must be a valid 3-letter code"),
            z.string().transform((val) => {
              if (val === "" || val === null || val === undefined)
                return undefined;
              // Extract 3-letter currency code from formats like "AUD - Australian Dollar"
              const match = val.match(/^([A-Z]{3})/);
              return match ? match[1] : undefined;
            }),
            z.literal("").transform(() => undefined),
            z.null().transform(() => undefined),
            z.undefined(),
          ])
          .optional(),

        // Performance Metrics with string-to-number transformation
        rating: z
          .union([
            z.number().min(0).max(5),
            z.string().transform((val) => {
              if (val === "" || val === null || val === undefined)
                return undefined;
              const num = parseFloat(val);
              return isNaN(num) ? undefined : Math.min(Math.max(num, 0), 5);
            }),
            z.null().transform(() => undefined),
            z.undefined(),
          ])
          .optional(),

        total_orders: z
          .union([
            z.number().min(0),
            z.string().transform((val) => {
              if (val === "" || val === null || val === undefined) return 0;
              const num = parseInt(val, 10);
              return isNaN(num) ? 0 : Math.max(num, 0);
            }),
            z.null().transform(() => 0),
            z.undefined().transform(() => 0),
          ])
          .optional()
          .default(0),

        completed_orders: z
          .union([
            z.number().min(0),
            z.string().transform((val) => {
              if (val === "" || val === null || val === undefined) return 0;
              const num = parseInt(val, 10);
              return isNaN(num) ? 0 : Math.max(num, 0);
            }),
            z.null().transform(() => 0),
            z.undefined().transform(() => 0),
          ])
          .optional()
          .default(0),

        // Categories with transformation for comma-separated strings
        categories: z
          .union([
            z.array(z.string()),
            z
              .string()
              .transform((val) =>
                val ? val.split(",").map((s) => s.trim()).filter(Boolean) : []
              ),
            z.null().transform(() => []),
            z.undefined().transform(() => []),
          ])
          .optional(),

        // Contacts array (from separate sheet)
        contacts: z
          .array(
            z.object({
              name: z.string().min(1, "Contact name is required"),
              email: z.string().optional(), // Remove email validation from Zod, handle in route
              phone: z.string().optional(),
              is_whatsapp: z.boolean().optional().default(false),
              is_primary: z.boolean().optional().default(false),
            })
          )
          .optional(),

        metadata: z.record(z.any()).optional(),
      })
    )
    .min(1, "At least one supplier is required"),
});

// Supplier activation/deactivation validator
export const PostAdminActivateSupplier = z.object({
  status: z.enum(["active", "inactive", "suspended"]),
  reason: z.string().optional(),
  activated_by: z.string().optional(),
});

// Query parameters for listing suppliers with comprehensive filtering
export const GetAdminSuppliersQuery = z.object({
  limit: z.coerce.number().min(1).max(100).default(20),
  offset: z.coerce.number().min(0).default(0),

  // Direct field filters
  status: z
    .enum(["Active", "Inactive", "Pending Approval", "Suspended", "Terminated"])
    .optional(),

  preference: z.enum(["Preferred", "Backup"]).optional(),
  supplier_name: z.string().optional(),
  categories: z.union([z.string(), z.array(z.string())]).optional(),

  // Contact-based filters
  primary_contact_email: z.string().optional(),
  primary_contact_person: z.string().optional(),

  // General search (backward compatibility)
  search: z.string().optional(),

  // Legacy fields for backward compatibility
  verification_status: z
    .enum(["verified", "unverified", "in_review"])
    .optional(),
  region: z.string().optional(),
  preferred_supplier: z.coerce.boolean().optional(),
  sort_by: z
    .enum(["name", "status", "created_at", "updated_at", "rating", "total_orders"])
    .default("status"),
  sort_order: z.enum(["asc", "desc"]).default("asc"),
});

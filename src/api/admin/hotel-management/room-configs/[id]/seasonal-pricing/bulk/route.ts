import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { HOTEL_PRICING_MODULE } from "../../../../../../../modules/hotel-management/hotel-pricing";
import { calculateTotalFromCostMargin } from "../../../../../../../modules/hotel-management/hotel-pricing/utils/cost-margin-calculator";

// Validation schema for bulk seasonal pricing (enhanced with cost/margin support)
export const PostAdminBulkSeasonalPricing = z.object({
  currency_code: z.string(),
  name: z.string(),
  start_date: z.string(),
  end_date: z.string(),
  // Default cost and margin values (same structure as weekday pricing API)
  default_values: z.object({
    gross_cost: z.number().min(0, "Default gross cost must be non-negative").nullable().optional(),
    fixed_margin: z.number().nullable().optional(),
    margin_percentage: z.number().min(0, "Default margin percentage must be non-negative").max(1000, "Default margin percentage cannot exceed 1000%").nullable().optional(),
    total: z.number().min(0, "Default total must be non-negative").nullable().optional(),
  }).optional(),
  weekday_rules: z.array(
    z.object({
      occupancy_type_id: z.string(),
      meal_plan_id: z.string(),
      weekday_prices: z.object({
        mon: z.number(),
        tue: z.number(),
        wed: z.number(),
        thu: z.number(),
        fri: z.number(),
        sat: z.number(),
        sun: z.number(),
      }),
      // Enhanced: Include default_values in each rule for backward compatibility
      default_values: z.object({
        gross_cost: z.number().min(0, "Gross cost must be non-negative").nullable().optional(),
        fixed_margin: z.number().nullable().optional(),
        margin_percentage: z.number().min(0, "Margin percentage must be non-negative").max(1000, "Margin percentage cannot exceed 1000%").nullable().optional(),
        total: z.number().min(0, "Total must be non-negative").nullable().optional(),
      }).optional(),
      // Enhanced: Include weekday_values for cost/margin breakdown per day (same as base pricing)
      weekday_values: z.object({
        mon: z.object({
          gross_cost: z.number().min(0, "Gross cost must be non-negative").nullable().optional(),
          fixed_margin: z.number().nullable().optional(),
          margin_percentage: z.number().min(0, "Margin percentage must be non-negative").max(1000, "Margin percentage cannot exceed 1000%").nullable().optional(),
        }).optional(),
        tue: z.object({
          gross_cost: z.number().min(0, "Gross cost must be non-negative").nullable().optional(),
          fixed_margin: z.number().nullable().optional(),
          margin_percentage: z.number().min(0, "Margin percentage must be non-negative").max(1000, "Margin percentage cannot exceed 1000%").nullable().optional(),
        }).optional(),
        wed: z.object({
          gross_cost: z.number().min(0, "Gross cost must be non-negative").nullable().optional(),
          fixed_margin: z.number().nullable().optional(),
          margin_percentage: z.number().min(0, "Margin percentage must be non-negative").max(1000, "Margin percentage cannot exceed 1000%").nullable().optional(),
        }).optional(),
        thu: z.object({
          gross_cost: z.number().min(0, "Gross cost must be non-negative").nullable().optional(),
          fixed_margin: z.number().nullable().optional(),
          margin_percentage: z.number().min(0, "Margin percentage must be non-negative").max(1000, "Margin percentage cannot exceed 1000%").nullable().optional(),
        }).optional(),
        fri: z.object({
          gross_cost: z.number().min(0, "Gross cost must be non-negative").nullable().optional(),
          fixed_margin: z.number().nullable().optional(),
          margin_percentage: z.number().min(0, "Margin percentage must be non-negative").max(1000, "Margin percentage cannot exceed 1000%").nullable().optional(),
        }).optional(),
        sat: z.object({
          gross_cost: z.number().min(0, "Gross cost must be non-negative").nullable().optional(),
          fixed_margin: z.number().nullable().optional(),
          margin_percentage: z.number().min(0, "Margin percentage must be non-negative").max(1000, "Margin percentage cannot exceed 1000%").nullable().optional(),
        }).optional(),
        sun: z.object({
          gross_cost: z.number().min(0, "Gross cost must be non-negative").nullable().optional(),
          fixed_margin: z.number().nullable().optional(),
          margin_percentage: z.number().min(0, "Margin percentage must be non-negative").max(1000, "Margin percentage cannot exceed 1000%").nullable().optional(),
        }).optional(),
      }).optional(),
    })
  ),
});

export type PostAdminBulkSeasonalPricingType = z.infer<typeof PostAdminBulkSeasonalPricing>;

/**
 * POST /admin/hotel-management/room-configs/:id/seasonal-pricing/bulk
 *
 * Bulk create or update seasonal pricing rules for a room configuration
 */
export const POST = async (req: MedusaRequest<PostAdminBulkSeasonalPricingType>, res: MedusaResponse) => {
  console.log(`[SEASONAL API] 🚀 BULK API CALLED - Room Config: ${req.params.id}`);
  console.log(`[SEASONAL API] 📥 Request body:`, JSON.stringify(req.body, null, 2));

  try {
    const roomConfigId = req.params.id;

    if (!roomConfigId) {
      return res.status(400).json({ message: "Room configuration ID is required" });
    }

    // Validate the request body
    const { currency_code, name, start_date, end_date, weekday_rules, default_values } = req.body;

    console.log(`Bulk creating seasonal pricing for room config: ${roomConfigId}`);
    console.log(`Season: ${name}, ${start_date} to ${end_date}`);
    console.log(`Rules: ${weekday_rules.length}`);
    if (default_values) {
      console.log(`[SEASONAL API] Processing default_values:`, default_values);
    }

    // Get the hotel pricing service
    const hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);

    // Enhanced validation: Check for duplicate seasons and overlapping date ranges
    const startDateObj = new Date(start_date);
    const endDateObj = new Date(end_date);

    // Validate date range
    if (startDateObj >= endDateObj) {
      return res.status(400).json({
        message: "Validation failed",
        error: "End date must be after start date.",
      });
    }

    // Get all base price rules for this room config to check for existing seasons
    const basePriceRules = await hotelPricingService.listBasePriceRules({
      room_config_id: roomConfigId,
    });

    // Check for existing seasons with the same name or overlapping dates
    const existingSeasons = new Map();

    for (const basePriceRule of basePriceRules) {
      const seasonalOverrides = await hotelPricingService.listSeasonalPriceRules({
        base_price_rule_id: basePriceRule.id,
      });

      for (const override of seasonalOverrides) {
        const metadata = override.metadata as any;
        const seasonName = override.name || metadata?.name || "Unnamed Season";
        const overrideCurrency = override.currency_code || basePriceRule.currency_code;

        // Only check seasons in the same currency
        if (overrideCurrency !== currency_code) {
          continue;
        }

        const key = `${seasonName}-${override.start_date}-${override.end_date}-${overrideCurrency}`;

        if (!existingSeasons.has(key)) {
          existingSeasons.set(key, {
            name: seasonName,
            start_date: override.start_date,
            end_date: override.end_date,
            currency_code: overrideCurrency,
          });
        }
      }
    }

    const existingSeasonsArray = Array.from(existingSeasons.values());

    // Check for duplicate name (case-insensitive)
    const duplicateName = existingSeasonsArray.find(
      season => season.name.toLowerCase() === name.toLowerCase()
    );

    if (duplicateName) {
      return res.status(400).json({
        message: "Validation failed",
        error: `A season with the name "${name}" already exists.`,
      });
    }

    // Check for overlapping date ranges
    for (const existingSeason of existingSeasonsArray) {
      const existingStartDate = new Date(existingSeason.start_date);
      const existingEndDate = new Date(existingSeason.end_date);

      // Skip validation if this is the same season being updated (same dates and currency)
      const isSameSeasonUpdate = (
        existingStartDate.toISOString().split('T')[0] === startDateObj.toISOString().split('T')[0] &&
        existingEndDate.toISOString().split('T')[0] === endDateObj.toISOString().split('T')[0] &&
        existingSeason.currency_code === currency_code
      );

      if (isSameSeasonUpdate) {
        console.log(`Skipping overlap validation for same season update: "${existingSeason.name}" -> "${name}"`);
        continue;
      }

      // Check if date ranges overlap
      const hasOverlap = (
        (startDateObj <= existingEndDate && startDateObj >= existingStartDate) ||
        (endDateObj <= existingEndDate && endDateObj >= existingStartDate) ||
        (startDateObj <= existingStartDate && endDateObj >= existingEndDate)
      );

      if (hasOverlap) {
        return res.status(400).json({
          message: "Validation failed",
          error: `Date range overlaps with existing season "${existingSeason.name}" (${existingSeason.start_date} to ${existingSeason.end_date}).`,
        });
      }
    }

    console.log(`✅ Validation passed for season "${name}" - no duplicates or overlaps found`);

    // Create the seasonal price rules
    const createdRules = [];

    try {
      // Get all existing base price rules for this room configuration filtered by currency
      const existingBasePriceRules = await hotelPricingService.listBasePriceRules({
        room_config_id: roomConfigId,
        currency_code: currency_code,
      });

      console.log(`Found ${existingBasePriceRules.length} base price rules for ${currency_code} currency`);

      // First, find any existing seasonal overrides instead of deleting them
      // We'll compare them with the new data and only update if there are changes
      const existingOverrides: Record<string, any[]> = {};

      // Also track all overlapping seasonal price rules that need to be made inactive
      const overlappingRules: Record<string, any[]> = {};

      try {
        // For each base price rule, we'll check if there are existing overrides
        for (const basePriceRule of existingBasePriceRules) {
          // Find existing overrides with the same parameters
          const overrides = await hotelPricingService.listSeasonalPriceRules({
            base_price_rule_id: basePriceRule.id,
          });

          // Filter to match the exact date range, name, and currency (for direct updates)
          const matchingOverrides = overrides.filter(override => {
            const overrideStartDate = override.start_date.toISOString().split('T')[0];
            const overrideEndDate = override.end_date.toISOString().split('T')[0];
            const requestStartDate = new Date(start_date).toISOString().split('T')[0];
            const requestEndDate = new Date(end_date).toISOString().split('T')[0];
            const overrideCurrency = override.currency_code || basePriceRule.currency_code || 'USD';

            return overrideStartDate === requestStartDate &&
                   overrideEndDate === requestEndDate &&
                   override.description === name &&
                   overrideCurrency === currency_code;
          });

          if (matchingOverrides && matchingOverrides.length > 0) {
            // Store existing overrides by base price rule ID
            existingOverrides[basePriceRule.id] = matchingOverrides;
          }

          // Find any other seasonal rules that overlap with the new date range
          // These will need to be made inactive
          const startDateObj = new Date(start_date);
          const endDateObj = new Date(end_date);

          const otherOverlappingRules = overrides.filter(override => {
            // Only consider overrides for the same currency
            const overrideCurrency = override.currency_code || basePriceRule.currency_code || 'USD';
            if (overrideCurrency !== currency_code) {
              return false;
            }

            // Skip the exact matches we already found
            const overrideStartDate = override.start_date.toISOString().split('T')[0];
            const overrideEndDate = override.end_date.toISOString().split('T')[0];
            const requestStartDate = new Date(start_date).toISOString().split('T')[0];
            const requestEndDate = new Date(end_date).toISOString().split('T')[0];

            const isExactMatch = overrideStartDate === requestStartDate &&
                               overrideEndDate === requestEndDate &&
                               override.description === name;

            if (isExactMatch) return false;

            // Check for date range overlap
            const ruleStart = override.start_date;
            const ruleEnd = override.end_date;

            // Check if the date ranges overlap
            const hasOverlap = (
              (startDateObj <= ruleEnd && startDateObj >= ruleStart) || // New start date is within existing rule
              (endDateObj <= ruleEnd && endDateObj >= ruleStart) ||     // New end date is within existing rule
              (startDateObj <= ruleStart && endDateObj >= ruleEnd)      // New rule completely contains existing rule
            );

            return hasOverlap;
          });

          if (otherOverlappingRules && otherOverlappingRules.length > 0) {
            overlappingRules[basePriceRule.id] = otherOverlappingRules;
          }
        }

        console.log(`Found ${Object.keys(existingOverrides).length} existing seasonal override groups`);
        console.log(`Found ${Object.values(overlappingRules).flat().length} overlapping seasonal rules that need to be updated`);
      } catch (error) {
        console.warn("Error finding existing seasonal overrides:", error);
        // Continue with creating new overrides
      }

      // For each weekday rule, find the corresponding base price rule and create a seasonal override
      for (const rule of weekday_rules) {
        console.log(`[SEASONAL API] 🔍 Processing rule for occupancy ${rule.occupancy_type_id}, meal plan ${rule.meal_plan_id}`);
        console.log(`[SEASONAL API] 📥 Input rule data:`, {
          default_values: rule.default_values,
          weekday_values: rule.weekday_values,
          weekday_prices: rule.weekday_prices
        });
        console.log(`[SEASONAL API] 📥 Global default_values:`, default_values);

        try {
          // Find the base price rule for this occupancy type and meal plan
          const basePriceRule = existingBasePriceRules.find(
            (bpr) =>
              bpr.occupancy_type_id === rule.occupancy_type_id &&
              bpr.meal_plan_id === rule.meal_plan_id
          );

          if (!basePriceRule) {
            console.warn(`No base price rule found for occupancy type ${rule.occupancy_type_id} and meal plan ${rule.meal_plan_id} in ${currency_code}`);
            continue;
          }

          // Update base price rule with cost/margin data if provided
          // Priority: rule-specific default_values > global default_values
          const ruleDefaultValues = rule.default_values || default_values;
          if (ruleDefaultValues) {
            console.log(`[SEASONAL API] Updating base price rule ${basePriceRule.id} with cost/margin data:`, ruleDefaultValues);

            // Prepare cost/margin fields for base price rule update (no currency conversion needed)
            const costMarginFields: { [key: string]: any } = {};

            if (ruleDefaultValues.gross_cost !== undefined && ruleDefaultValues.gross_cost !== null) {
              costMarginFields.default_gross_cost = ruleDefaultValues.gross_cost;
              console.log(`[SEASONAL API] Setting default_gross_cost: ${ruleDefaultValues.gross_cost} CHF`);
            }
            if (ruleDefaultValues.fixed_margin !== undefined && ruleDefaultValues.fixed_margin !== null) {
              costMarginFields.default_fixed_margin = ruleDefaultValues.fixed_margin;
              console.log(`[SEASONAL API] Setting default_fixed_margin: ${ruleDefaultValues.fixed_margin} CHF`);
            }
            if (ruleDefaultValues.margin_percentage !== undefined && ruleDefaultValues.margin_percentage !== null) {
              costMarginFields.default_margin_percentage = ruleDefaultValues.margin_percentage;
              console.log(`[SEASONAL API] Setting default_margin_percentage: ${ruleDefaultValues.margin_percentage}%`);
            }

            // Handle default total - prioritize provided value, then calculate if needed
            if (ruleDefaultValues.total !== undefined && ruleDefaultValues.total !== null) {
              console.log(`[SEASONAL API] Using provided default_total: ${ruleDefaultValues.total} CHF`);
              costMarginFields.default_total = ruleDefaultValues.total;
              console.log(`[SEASONAL API] Setting default_total: ${ruleDefaultValues.total} CHF`);
            } else if (ruleDefaultValues.gross_cost) {
              // Calculate default total if not provided but cost data is available
              const defaultTotal = calculateTotalFromCostMargin({
                gross_cost: ruleDefaultValues.gross_cost,
                fixed_margin: ruleDefaultValues.fixed_margin,
                margin_percentage: ruleDefaultValues.margin_percentage
              });
              if (defaultTotal !== null) {
                console.log(`[SEASONAL API] Calculated default_total: ${defaultTotal} CHF`);
                costMarginFields.default_total = defaultTotal;
                console.log(`[SEASONAL API] Setting calculated default_total: ${defaultTotal} CHF`);
              }
            }

            // ❌ REMOVED: Do NOT update base price rule from seasonal pricing
            // Seasonal pricing should only store overrides in metadata, never modify base price rule
            console.log(`[SEASONAL API] ✅ Default values will be stored in seasonal metadata only (not modifying base price rule)`);
            if (Object.keys(costMarginFields).length > 0) {
              console.log(`[SEASONAL API] 📋 Default values to store in metadata:`, costMarginFields);
            }
          }

          // Process weekday_values for cost/margin data (same logic as base pricing)
          if (rule.weekday_values) {
            console.log(`[SEASONAL API] Processing weekday_values for cost/margin data...`);

            // Helper function to process individual weekday cost/margin values
            const processWeekdayValue = (dayData: any, dayName: string, fallbackPrice: number) => {
              const result: { [key: string]: any } = {};

              if (!dayData) {
                console.log(`[SEASONAL API] No ${dayName} data provided, skipping`);
                return result;
              }

              console.log(`[SEASONAL API] DEBUG - ${dayName} values:`, dayData);

              // Store cost/margin values as-is (no currency conversion needed)
              const grossCostValue = dayData.gross_cost !== undefined && dayData.gross_cost !== null ? dayData.gross_cost : 0;
              const fixedMarginValue = dayData.fixed_margin !== undefined && dayData.fixed_margin !== null ? dayData.fixed_margin : 0;
              const marginPercentageValue = dayData.margin_percentage !== undefined && dayData.margin_percentage !== null ? dayData.margin_percentage : 0;

              result[`${dayName.toLowerCase()}_gross_cost`] = grossCostValue;
              result[`${dayName.toLowerCase()}_fixed_margin`] = fixedMarginValue;
              result[`${dayName.toLowerCase()}_margin_percentage`] = marginPercentageValue;

              console.log(`[SEASONAL API] 🔍 ${dayName} cost/margin storage values:`, {
                original: dayData,
                stored: { gross_cost: grossCostValue, fixed_margin: fixedMarginValue, margin_percentage: marginPercentageValue },
                fieldNames: [`${dayName.toLowerCase()}_gross_cost`, `${dayName.toLowerCase()}_fixed_margin`, `${dayName.toLowerCase()}_margin_percentage`]
              });

              // ARCHITECTURE: Always use provided weekday_prices for price field
              // Cost/margin data is stored separately and doesn't override explicit prices
              result[`${dayName.toLowerCase()}_price`] = fallbackPrice;
              console.log(`[SEASONAL API] 💰 ${dayName} using provided weekday price: ${fallbackPrice} ${currency_code}`);

              console.log(`[SEASONAL API] 📊 ${dayName} FINAL DATABASE VALUES:`, result);

              return result;
            };

            // Process all weekday values using the helper function
            console.log(`[SEASONAL API] 🔄 Processing weekday values...`);
            const weekdayCostMarginFields: { [key: string]: any } = {};

            if (rule.weekday_values.mon) {
              Object.assign(weekdayCostMarginFields, processWeekdayValue(rule.weekday_values.mon, 'Monday', rule.weekday_prices.mon));
            }
            if (rule.weekday_values.tue) {
              Object.assign(weekdayCostMarginFields, processWeekdayValue(rule.weekday_values.tue, 'Tuesday', rule.weekday_prices.tue));
            }
            if (rule.weekday_values.wed) {
              Object.assign(weekdayCostMarginFields, processWeekdayValue(rule.weekday_values.wed, 'Wednesday', rule.weekday_prices.wed));
            }
            if (rule.weekday_values.thu) {
              Object.assign(weekdayCostMarginFields, processWeekdayValue(rule.weekday_values.thu, 'Thursday', rule.weekday_prices.thu));
            }
            if (rule.weekday_values.fri) {
              Object.assign(weekdayCostMarginFields, processWeekdayValue(rule.weekday_values.fri, 'Friday', rule.weekday_prices.fri));
            }
            if (rule.weekday_values.sat) {
              Object.assign(weekdayCostMarginFields, processWeekdayValue(rule.weekday_values.sat, 'Saturday', rule.weekday_prices.sat));
            }
            if (rule.weekday_values.sun) {
              Object.assign(weekdayCostMarginFields, processWeekdayValue(rule.weekday_values.sun, 'Sunday', rule.weekday_prices.sun));
            }

            // ❌ REMOVED: Do NOT update base price rule from seasonal pricing
            // Seasonal pricing should only store overrides in metadata, never modify base price rule
            console.log(`[SEASONAL API] ✅ Weekday values will be stored in seasonal metadata only (not modifying base price rule)`);
            if (Object.keys(weekdayCostMarginFields).length > 0) {
              console.log(`[SEASONAL API] 📋 Weekday values to store in metadata:`, weekdayCostMarginFields);
            }
          }

          // Check if there's an existing override for this base price rule
          let savedOverride: any;
          const existingRuleOverrides = existingOverrides[basePriceRule.id] || [];

          // Try to find an existing override for this base price rule with matching dates and name
          console.log(`Looking for existing override: base_price_rule_id=${basePriceRule.id}, name=${name}, start_date=${start_date}, end_date=${end_date}`);
          console.log(`Available overrides for this base price rule:`, existingRuleOverrides.map(o => `${o.id}: ${o.description}, ${o.start_date} to ${o.end_date}`));

          const existingOverride = existingRuleOverrides.find((override: any) => {
            const matchesRule = override.base_price_rule_id === basePriceRule.id;
            const matchesName = override.description === name;
            const matchesStartDate = new Date(override.start_date).toISOString() === new Date(start_date).toISOString();
            const matchesEndDate = new Date(override.end_date).toISOString() === new Date(end_date).toISOString();

            console.log(`Checking override ${override.id}: rule=${matchesRule}, name=${matchesName}, start=${matchesStartDate}, end=${matchesEndDate}`);
            return matchesRule && matchesName && matchesStartDate && matchesEndDate;
          });

          console.log(`Found existing override:`, existingOverride ? existingOverride.id : 'none');

          if (existingOverride) {
            // Check if there are any changes
            const currentAmount = existingOverride.amount;
            const newAmount = rule.weekday_prices.mon;
            const currentWeekdayPrices = existingOverride.metadata?.weekday_prices || {};
            const hasWeekdayPriceChanges = Object.keys(rule.weekday_prices).some(
              day => rule.weekday_prices[day] !== currentWeekdayPrices[day]
            );

            if (currentAmount !== newAmount || hasWeekdayPriceChanges) {
              console.log(`Updating existing seasonal override for base price rule ${basePriceRule.id}`);

              try {
                // Update the existing override using the SeasonalPriceRule entity
                // Increase the priority to ensure this updated rule takes precedence
                // Use a simpler priority scheme - current date in YYYYMMDD format
                const now = new Date();
                const priorityValue = parseInt(
                  `${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}`
                );
                const existingMetadata = existingOverride.metadata || {};

                const updatedOverrides = await hotelPricingService.updateSeasonalPriceRules([{
                  id: existingOverride.id,
                  amount: rule.weekday_prices.mon,
                  priority: priorityValue, // Update priority using date-based value
                  metadata: {
                    ...existingMetadata,
                    // Store weekday prices as-is (no currency conversion needed)
                    weekday_prices: {
                      mon: rule.weekday_prices.mon || 0,
                      tue: rule.weekday_prices.tue || 0,
                      wed: rule.weekday_prices.wed || 0,
                      thu: rule.weekday_prices.thu || 0,
                      fri: rule.weekday_prices.fri || 0,
                      sat: rule.weekday_prices.sat || 0,
                      sun: rule.weekday_prices.sun || 0,
                    },
                    // Include cost/margin data for reference (store as-is)
                    default_values: {
                      gross_cost: (ruleDefaultValues || default_values)?.gross_cost || 0,
                      fixed_margin: (ruleDefaultValues || default_values)?.fixed_margin || 0,
                      margin_percentage: (ruleDefaultValues || default_values)?.margin_percentage || 0,
                      total: (ruleDefaultValues || default_values)?.total || 0,
                    },
                    weekday_values: {
                      mon: {
                        gross_cost: rule.weekday_values?.mon?.gross_cost || 0,
                        fixed_margin: rule.weekday_values?.mon?.fixed_margin || 0,
                        margin_percentage: rule.weekday_values?.mon?.margin_percentage || 0,
                      },
                      tue: {
                        gross_cost: rule.weekday_values?.tue?.gross_cost || 0,
                        fixed_margin: rule.weekday_values?.tue?.fixed_margin || 0,
                        margin_percentage: rule.weekday_values?.tue?.margin_percentage || 0,
                      },
                      wed: {
                        gross_cost: rule.weekday_values?.wed?.gross_cost || 0,
                        fixed_margin: rule.weekday_values?.wed?.fixed_margin || 0,
                        margin_percentage: rule.weekday_values?.wed?.margin_percentage || 0,
                      },
                      thu: {
                        gross_cost: rule.weekday_values?.thu?.gross_cost || 0,
                        fixed_margin: rule.weekday_values?.thu?.fixed_margin || 0,
                        margin_percentage: rule.weekday_values?.thu?.margin_percentage || 0,
                      },
                      fri: {
                        gross_cost: rule.weekday_values?.fri?.gross_cost || 0,
                        fixed_margin: rule.weekday_values?.fri?.fixed_margin || 0,
                        margin_percentage: rule.weekday_values?.fri?.margin_percentage || 0,
                      },
                      sat: {
                        gross_cost: rule.weekday_values?.sat?.gross_cost || 0,
                        fixed_margin: rule.weekday_values?.sat?.fixed_margin || 0,
                        margin_percentage: rule.weekday_values?.sat?.margin_percentage || 0,
                      },
                      sun: {
                        gross_cost: rule.weekday_values?.sun?.gross_cost || 0,
                        fixed_margin: rule.weekday_values?.sun?.fixed_margin || 0,
                        margin_percentage: rule.weekday_values?.sun?.margin_percentage || 0,
                      },
                    },
                    updated_at: new Date().toISOString(),
                    is_active: true // Ensure it's marked as active
                  }
                }]);

                savedOverride = updatedOverrides[0];

                // Also handle any overlapping rules by setting them to inactive
                const overlappingRulesForBasePriceRule = overlappingRules[basePriceRule.id] || [];

                if (overlappingRulesForBasePriceRule.length > 0) {
                  console.log(`Setting ${overlappingRulesForBasePriceRule.length} overlapping rules to inactive for base price rule ${basePriceRule.id}`);

                  // Update each overlapping rule to be inactive
                  for (const overlappingRule of overlappingRulesForBasePriceRule) {
                    try {
                      // Skip the rule we just updated
                      if (overlappingRule.id === existingOverride.id) continue;

                      // Keep the original metadata but update the is_active flag
                      const updatedMetadata = {
                        ...(overlappingRule.metadata || {}),
                        is_active: false,
                        deactivated_at: new Date().toISOString(),
                        deactivated_by: savedOverride.id // Reference to the rule that replaced this one
                      };

                      // Update the rule to be inactive with a lower priority
                      // Set priority to a lower value (1) for inactive rules
                      await hotelPricingService.updateSeasonalPriceRules([{
                        id: overlappingRule.id,
                        priority: 1, // Set to a low priority value for inactive rules
                        metadata: updatedMetadata
                      }]);

                      console.log(`Set rule ${overlappingRule.id} to inactive`);
                    } catch (error) {
                      console.error(`Error updating overlapping rule ${overlappingRule.id}:`, error);
                    }
                  }
                }
              } catch (error) {
                console.error(`Error updating seasonal override: ${error}`);
                // Fall back to using the existing override
                savedOverride = existingOverride;
              }
            } else {
              console.log(`No changes for seasonal override ${existingOverride.id}, skipping update`);
              savedOverride = existingOverride;
            }
          } else {
            console.log(`Creating new seasonal override for base price rule ${basePriceRule.id}`);

            // Set a higher priority for the new rule
            // Use a simpler priority scheme - current date in YYYYMMDD format
            // This ensures newer rules have higher priority without using the full timestamp
            const now = new Date();
            const priorityValue = parseInt(
              `${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}`
            );

            // Create a new seasonal override with priority
            const savedOverrides = await hotelPricingService.createSeasonalPriceRules([{
              base_price_rule_id: basePriceRule.id,
              start_date: new Date(start_date),
              end_date: new Date(end_date),
              amount: rule.weekday_prices.mon, // Use Monday's price as the base amount
              currency_code,
              description: name,
              priority: priorityValue, // Use date-based priority to ensure newer rules have higher priority
              metadata: {
                // Store weekday prices as-is (no currency conversion needed)
                weekday_prices: {
                  mon: rule.weekday_prices.mon || 0,
                  tue: rule.weekday_prices.tue || 0,
                  wed: rule.weekday_prices.wed || 0,
                  thu: rule.weekday_prices.thu || 0,
                  fri: rule.weekday_prices.fri || 0,
                  sat: rule.weekday_prices.sat || 0,
                  sun: rule.weekday_prices.sun || 0,
                },
                // Include cost/margin data for reference (store as-is)
                default_values: (() => {
                  const sourceValues = ruleDefaultValues || default_values;
                  const storedValues = {
                    gross_cost: sourceValues?.gross_cost || 0,
                    fixed_margin: sourceValues?.fixed_margin || 0,
                    margin_percentage: sourceValues?.margin_percentage || 0,
                    total: sourceValues?.total || 0,
                  };
                  console.log(`[SEASONAL API] 💾 Storing default_values in metadata:`, {
                    ruleDefaultValues,
                    globalDefaultValues: default_values,
                    sourceValues,
                    storedValues
                  });
                  return storedValues;
                })(),
                weekday_values: {
                  mon: {
                    gross_cost: rule.weekday_values?.mon?.gross_cost || 0,
                    fixed_margin: rule.weekday_values?.mon?.fixed_margin || 0,
                    margin_percentage: rule.weekday_values?.mon?.margin_percentage || 0,
                  },
                  tue: {
                    gross_cost: rule.weekday_values?.tue?.gross_cost || 0,
                    fixed_margin: rule.weekday_values?.tue?.fixed_margin || 0,
                    margin_percentage: rule.weekday_values?.tue?.margin_percentage || 0,
                  },
                  wed: {
                    gross_cost: rule.weekday_values?.wed?.gross_cost || 0,
                    fixed_margin: rule.weekday_values?.wed?.fixed_margin || 0,
                    margin_percentage: rule.weekday_values?.wed?.margin_percentage || 0,
                  },
                  thu: {
                    gross_cost: rule.weekday_values?.thu?.gross_cost || 0,
                    fixed_margin: rule.weekday_values?.thu?.fixed_margin || 0,
                    margin_percentage: rule.weekday_values?.thu?.margin_percentage || 0,
                  },
                  fri: {
                    gross_cost: rule.weekday_values?.fri?.gross_cost || 0,
                    fixed_margin: rule.weekday_values?.fri?.fixed_margin || 0,
                    margin_percentage: rule.weekday_values?.fri?.margin_percentage || 0,
                  },
                  sat: {
                    gross_cost: rule.weekday_values?.sat?.gross_cost || 0,
                    fixed_margin: rule.weekday_values?.sat?.fixed_margin || 0,
                    margin_percentage: rule.weekday_values?.sat?.margin_percentage || 0,
                  },
                  sun: {
                    gross_cost: rule.weekday_values?.sun?.gross_cost || 0,
                    fixed_margin: rule.weekday_values?.sun?.fixed_margin || 0,
                    margin_percentage: rule.weekday_values?.sun?.margin_percentage || 0,
                  },
                },
                created_at: new Date().toISOString(),
                is_active: true
              },
            }]);

            // Get the first saved override
            savedOverride = savedOverrides[0];

            // Now handle any overlapping rules by setting them to inactive
            const overlappingRulesForBasePriceRule = overlappingRules[basePriceRule.id] || [];

            if (overlappingRulesForBasePriceRule.length > 0) {
              console.log(`Setting ${overlappingRulesForBasePriceRule.length} overlapping rules to inactive for base price rule ${basePriceRule.id}`);

              // Update each overlapping rule to be inactive
              for (const overlappingRule of overlappingRulesForBasePriceRule) {
                try {
                  // Keep the original metadata but update the is_active flag
                  const updatedMetadata = {
                    ...(overlappingRule.metadata || {}),
                    is_active: false,
                    deactivated_at: new Date().toISOString(),
                    deactivated_by: savedOverride.id // Reference to the rule that replaced this one
                  };

                  // Update the rule to be inactive with a lower priority
                  // Set priority to a lower value (1) for inactive rules
                  await hotelPricingService.updateSeasonalPriceRules([{
                    id: overlappingRule.id,
                    priority: 1, // Set to a low priority value for inactive rules
                    metadata: updatedMetadata
                  }]);

                  console.log(`Set rule ${overlappingRule.id} to inactive`);
                } catch (error) {
                  console.error(`Error updating overlapping rule ${overlappingRule.id}:`, error);
                }
              }
            }
          }

          createdRules.push({
            id: savedOverride.id,
            base_price_rule_id: basePriceRule.id,
            occupancy_type_id: rule.occupancy_type_id,
            meal_plan_id: rule.meal_plan_id,
            start_date,
            end_date,
            name,
            amount: rule.weekday_prices.mon,
            currency_code,
            weekday_prices: rule.weekday_prices,
          });
        } catch (error) {
          console.error(`Error creating seasonal override for occupancy type ${rule.occupancy_type_id} and meal plan ${rule.meal_plan_id}:`, error);
          // Continue with other rules
        }
      }
    } catch (error) {
      console.error("Error in bulk create operation:", error);
      return res.status(500).json({
        message: "An error occurred while bulk creating seasonal pricing rules",
        error: error instanceof Error ? error.message : String(error),
      });
    }

    // Format the response with enhanced cost/margin data
    const formattedRules = await Promise.all(createdRules.map(async (rule) => {
      try {
        // Fetch the actual saved rule with metadata from database
        console.log(`[SEASONAL API] 🔍 Attempting to fetch saved rule ${rule.id} for response formatting`);
        const savedRule = await hotelPricingService.retrieveSeasonalPriceRule(rule.id);
        console.log(`[SEASONAL API] ✅ Successfully fetched saved rule ${rule.id}:`, {
          id: savedRule.id,
          hasMetadata: !!savedRule.metadata,
          metadataKeys: savedRule.metadata ? Object.keys(savedRule.metadata) : []
        });

        // Extract weekday prices from metadata (with proper zero value handling)
        const metadata = savedRule.metadata as any;
        const weekdayPrices = metadata?.weekday_prices || {};

        return {
          id: rule.id,
          base_price_rule_id: rule.base_price_rule_id,
          start_date: rule.start_date,
          end_date: rule.end_date,
          name: rule.description,
          priority: rule.priority,
          currency_code: rule.currency_code,
          // Enhanced: Include weekday prices with proper zero value handling
          weekday_prices: {
            mon: weekdayPrices.mon !== null && weekdayPrices.mon !== undefined ? weekdayPrices.mon : rule.amount,
            tue: weekdayPrices.tue !== null && weekdayPrices.tue !== undefined ? weekdayPrices.tue : rule.amount,
            wed: weekdayPrices.wed !== null && weekdayPrices.wed !== undefined ? weekdayPrices.wed : rule.amount,
            thu: weekdayPrices.thu !== null && weekdayPrices.thu !== undefined ? weekdayPrices.thu : rule.amount,
            fri: weekdayPrices.fri !== null && weekdayPrices.fri !== undefined ? weekdayPrices.fri : rule.amount,
            sat: weekdayPrices.sat !== null && weekdayPrices.sat !== undefined ? weekdayPrices.sat : rule.amount,
            sun: weekdayPrices.sun !== null && weekdayPrices.sun !== undefined ? weekdayPrices.sun : rule.amount,
          },
          // Enhanced: Include cost/margin data from seasonal metadata (convert from cents to display format)
          default_values: (() => {
            const metadataValues = metadata?.default_values;
            const responseValues = {
              gross_cost: metadataValues?.gross_cost || 0,
              fixed_margin: metadataValues?.fixed_margin || 0,
              margin_percentage: metadataValues?.margin_percentage || 0,
              total: metadataValues?.total || 0,
            };
            console.log(`[SEASONAL API] 📤 Response default_values conversion:`, {
              ruleId: savedRule.id,
              metadataValues,
              responseValues
            });
            return responseValues;
          })(),
          // Enhanced: Include weekday cost/margin data from seasonal metadata (convert from cents to display format)
          weekday_values: {
            mon: {
              gross_cost: metadata?.weekday_values?.mon?.gross_cost || 0,
              fixed_margin: metadata?.weekday_values?.mon?.fixed_margin || 0,
              margin_percentage: metadata?.weekday_values?.mon?.margin_percentage || 0,
            },
            tue: {
              gross_cost: metadata?.weekday_values?.tue?.gross_cost || 0,
              fixed_margin: metadata?.weekday_values?.tue?.fixed_margin || 0,
              margin_percentage: metadata?.weekday_values?.tue?.margin_percentage || 0,
            },
            wed: {
              gross_cost: metadata?.weekday_values?.wed?.gross_cost || 0,
              fixed_margin: metadata?.weekday_values?.wed?.fixed_margin || 0,
              margin_percentage: metadata?.weekday_values?.wed?.margin_percentage || 0,
            },
            thu: {
              gross_cost: metadata?.weekday_values?.thu?.gross_cost || 0,
              fixed_margin: metadata?.weekday_values?.thu?.fixed_margin || 0,
              margin_percentage: metadata?.weekday_values?.thu?.margin_percentage || 0,
            },
            fri: {
              gross_cost: metadata?.weekday_values?.fri?.gross_cost || 0,
              fixed_margin: metadata?.weekday_values?.fri?.fixed_margin || 0,
              margin_percentage: metadata?.weekday_values?.fri?.margin_percentage || 0,
            },
            sat: {
              gross_cost: metadata?.weekday_values?.sat?.gross_cost || 0,
              fixed_margin: metadata?.weekday_values?.sat?.fixed_margin || 0,
              margin_percentage: metadata?.weekday_values?.sat?.margin_percentage || 0,
            },
            sun: {
              gross_cost: metadata?.weekday_values?.sun?.gross_cost || 0,
              fixed_margin: metadata?.weekday_values?.sun?.fixed_margin || 0,
              margin_percentage: metadata?.weekday_values?.sun?.margin_percentage || 0,
            },
          },
          created_at: rule.created_at,
          updated_at: rule.updated_at,
        };
      } catch (error) {
        console.error(`Error formatting seasonal rule ${rule.id}:`, error);
        // Fallback to basic rule data if formatting fails
        return {
          id: rule.id,
          base_price_rule_id: rule.base_price_rule_id,
          start_date: rule.start_date,
          end_date: rule.end_date,
          name: rule.description,
          priority: rule.priority,
          currency_code: rule.currency_code,
          weekday_prices: rule.metadata?.weekday_prices || {},
          created_at: rule.created_at,
          updated_at: rule.updated_at,
        };
      }
    }));

    // Return the enhanced formatted rules
    console.log(`[SEASONAL API] 📤 Final response:`, JSON.stringify(formattedRules, null, 2));

    res.status(201).json({
      seasonal_rules: formattedRules,
    });
  } catch (error) {
    console.error("Error processing request:", error);
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to create seasonal pricing rules",
    });
  }
};

import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { HOTEL_PRICING_MODULE } from "../../../../../../../modules/hotel-management/hotel-pricing";
import { Modules } from "@camped-ai/framework/utils";
import { CurrencyValidationService } from "../../../../../../../modules/hotel-management/hotel-pricing/services/currency-validation";
import {
  calculateTotalFromCostMargin,
  validateCostMarginData,
  type DayOfWeek
} from "../../../../../../../modules/hotel-management/hotel-pricing/utils/cost-margin-calculator";

// Validation schema for cost and margin data
const CostMarginSchema = z.object({
  gross_cost: z.number().min(0, "Gross cost must be non-negative").nullable().optional(),
  fixed_margin: z.number().nullable().optional(),
  margin_percentage: z.union([
    z.number().min(0, "Margin percentage must be non-negative").max(1000, "Margin percentage cannot exceed 1000%"),
    z.string().transform((val) => parseFloat(val)).refine((val) => !isNaN(val) && val >= 0 && val <= 1000, "Margin percentage must be a valid number between 0 and 1000")
  ]).nullable().optional(),
});

// Validation schema for weekday cost and margin data
const WeekdayCostMarginSchema = z.object({
  mon: CostMarginSchema.optional(),
  tue: CostMarginSchema.optional(),
  wed: CostMarginSchema.optional(),
  thu: CostMarginSchema.optional(),
  fri: CostMarginSchema.optional(),
  sat: CostMarginSchema.optional(),
  sun: CostMarginSchema.optional(),
});

// Validation schema for bulk weekday pricing (enhanced with cost/margin support)
export const PostAdminBulkWeekdayPricing = z.object({
  currency_code: z.string().refine(
    (code) => CurrencyValidationService.isValidCurrencyFormat(code),
    { message: "Currency code must be a valid 3-letter ISO 4217 code" }
  ),
  // Default cost and margin values
  default_values: z.object({
    gross_cost: z.number().min(0, "Default gross cost must be non-negative").nullable().optional(),
    fixed_margin: z.number().nullable().optional(),
    margin_percentage: z.union([
      z.number().min(0, "Default margin percentage must be non-negative").max(1000, "Default margin percentage cannot exceed 1000%"),
      z.string().transform((val) => parseFloat(val)).refine((val) => !isNaN(val) && val >= 0 && val <= 1000, "Default margin percentage must be a valid number between 0 and 1000")
    ]).nullable().optional(),
    total: z.number().min(0, "Default total must be non-negative").nullable().optional(),
  }).optional(),
  weekday_rules: z.array(
    z.object({
      occupancy_type_id: z.string(),
      meal_plan_id: z.string(),
      // Legacy weekday prices (for backward compatibility)
      weekday_prices: z.object({
        mon: z.number().min(0, "Price must be non-negative"),
        tue: z.number().min(0, "Price must be non-negative"),
        wed: z.number().min(0, "Price must be non-negative"),
        thu: z.number().min(0, "Price must be non-negative"),
        fri: z.number().min(0, "Price must be non-negative"),
        sat: z.number().min(0, "Price must be non-negative"),
        sun: z.number().min(0, "Price must be non-negative"),
      }).optional(),
      // New cost and margin data (legacy structure)
      cost_margin_data: WeekdayCostMarginSchema.optional(),
      // New restructured default values (per rule)
      default_values: z.object({
        gross_cost: z.number().min(0, "Gross cost must be non-negative").nullable().optional(),
        fixed_margin: z.number().nullable().optional(),
        margin_percentage: z.union([
          z.number().min(0, "Margin percentage must be non-negative").max(1000, "Margin percentage cannot exceed 1000%"),
          z.string().transform((val) => parseFloat(val)).refine((val) => !isNaN(val) && val >= 0 && val <= 1000, "Margin percentage must be a valid number between 0 and 1000")
        ]).nullable().optional(),
        total: z.number().min(0, "Total must be non-negative").nullable().optional(),
      }).optional(),
      // Weekday-specific cost and margin values
      // NOTE: 'total' field should NOT be included - backend calculates this automatically
      weekday_values: z.object({
        mon: z.object({
          gross_cost: z.number().min(0, "Gross cost must be non-negative").nullable().optional(),
          fixed_margin: z.number().nullable().optional(),
          margin_percentage: z.union([
            z.number().min(0, "Margin percentage must be non-negative").max(1000, "Margin percentage cannot exceed 1000%"),
            z.string().transform((val) => parseFloat(val)).refine((val) => !isNaN(val) && val >= 0 && val <= 1000, "Margin percentage must be a valid number between 0 and 1000")
          ]).nullable().optional(),
        }).optional(),
        tue: z.object({
          gross_cost: z.number().min(0, "Gross cost must be non-negative").nullable().optional(),
          fixed_margin: z.number().nullable().optional(),
          margin_percentage: z.union([
            z.number().min(0, "Margin percentage must be non-negative").max(1000, "Margin percentage cannot exceed 1000%"),
            z.string().transform((val) => parseFloat(val)).refine((val) => !isNaN(val) && val >= 0 && val <= 1000, "Margin percentage must be a valid number between 0 and 1000")
          ]).nullable().optional(),
        }).optional(),
        wed: z.object({
          gross_cost: z.number().min(0, "Gross cost must be non-negative").nullable().optional(),
          fixed_margin: z.number().nullable().optional(),
          margin_percentage: z.union([
            z.number().min(0, "Margin percentage must be non-negative").max(1000, "Margin percentage cannot exceed 1000%"),
            z.string().transform((val) => parseFloat(val)).refine((val) => !isNaN(val) && val >= 0 && val <= 1000, "Margin percentage must be a valid number between 0 and 1000")
          ]).nullable().optional(),
        }).optional(),
        thu: z.object({
          gross_cost: z.number().min(0, "Gross cost must be non-negative").nullable().optional(),
          fixed_margin: z.number().nullable().optional(),
          margin_percentage: z.union([
            z.number().min(0, "Margin percentage must be non-negative").max(1000, "Margin percentage cannot exceed 1000%"),
            z.string().transform((val) => parseFloat(val)).refine((val) => !isNaN(val) && val >= 0 && val <= 1000, "Margin percentage must be a valid number between 0 and 1000")
          ]).nullable().optional(),
        }).optional(),
        fri: z.object({
          gross_cost: z.number().min(0, "Gross cost must be non-negative").nullable().optional(),
          fixed_margin: z.number().nullable().optional(),
          margin_percentage: z.union([
            z.number().min(0, "Margin percentage must be non-negative").max(1000, "Margin percentage cannot exceed 1000%"),
            z.string().transform((val) => parseFloat(val)).refine((val) => !isNaN(val) && val >= 0 && val <= 1000, "Margin percentage must be a valid number between 0 and 1000")
          ]).nullable().optional(),
        }).optional(),
        sat: z.object({
          gross_cost: z.number().min(0, "Gross cost must be non-negative").nullable().optional(),
          fixed_margin: z.number().nullable().optional(),
          margin_percentage: z.union([
            z.number().min(0, "Margin percentage must be non-negative").max(1000, "Margin percentage cannot exceed 1000%"),
            z.string().transform((val) => parseFloat(val)).refine((val) => !isNaN(val) && val >= 0 && val <= 1000, "Margin percentage must be a valid number between 0 and 1000")
          ]).nullable().optional(),
        }).optional(),
        sun: z.object({
          gross_cost: z.number().min(0, "Gross cost must be non-negative").nullable().optional(),
          fixed_margin: z.number().nullable().optional(),
          margin_percentage: z.union([
            z.number().min(0, "Margin percentage must be non-negative").max(1000, "Margin percentage cannot exceed 1000%"),
            z.string().transform((val) => parseFloat(val)).refine((val) => !isNaN(val) && val >= 0 && val <= 1000, "Margin percentage must be a valid number between 0 and 1000")
          ]).nullable().optional(),
        }).optional(),
      }).optional(),
    })
  ),
});

export type PostAdminBulkWeekdayPricingType = z.infer<typeof PostAdminBulkWeekdayPricing>;

/**
 * POST /admin/hotel-management/room-configs/:id/weekday-pricing/bulk
 *
 * Bulk create or update weekday pricing rules for a room configuration
 */
export const POST = async (req: MedusaRequest<PostAdminBulkWeekdayPricingType>, res: MedusaResponse) => {
  try {
    const roomConfigId = req.params.id;

    if (!roomConfigId) {
      return res.status(400).json({ message: "Room configuration ID is required" });
    }

    // Validate the request body
    const { currency_code, weekday_rules, default_values } = req.body;

    console.log(`Bulk upserting ${weekday_rules.length} weekday pricing rules for room config: ${roomConfigId}`);

    // Get the query service for currency validation
    const query = req.scope.resolve("query");

    // Validate and normalize currency code
    const validatedCurrencyCode = await CurrencyValidationService.validateAndNormalizeCurrency(
      currency_code,
      query
    );

    // Get the hotel pricing service
    const hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);

    // Extract the hotel ID from the room config
    const productService = req.scope.resolve(Modules.PRODUCT);
    const product = await productService.retrieveProduct(roomConfigId, {
      relations: ["categories"],
    });

    // Ensure hotelId is a string
    const hotelId = String(product.metadata?.hotel_id || "hotel_default");

    // Create the new base price rules
    const createdRules = [];

    try {
      // Get all existing base price rules for this room configuration
      const existingRules = await hotelPricingService.listBasePriceRules({
        room_config_id: roomConfigId,
      });

      // Create a map of existing rules by occupancy_type_id, meal_plan_id, and currency_code
      const existingRulesMap = new Map();
      for (const rule of existingRules) {
        const ruleCurrency = rule.currency_code || 'USD'; // Default to USD for legacy rules
        const key = `${rule.occupancy_type_id}_${rule.meal_plan_id || 'null'}_${ruleCurrency}`;
        existingRulesMap.set(key, rule);
      }

      console.log(`Found ${existingRules.length} existing base price rules for room config: ${roomConfigId}`);

      // Process each rule in the request
      for (const rule of weekday_rules) {
        try {
          // Calculate pricing data - prioritize cost+margin over legacy prices
          let amount: number;
          let calculatedPrices: { [key: string]: number } = {};
          let costMarginFields: { [key: string]: any } = {};

          // Helper function to calculate price from cost/margin data
          const calculatePriceFromCostMargin = (dayData: any) => {
            if (!dayData || !dayData.gross_cost) return null;
            return calculateTotalFromCostMargin({
              gross_cost: dayData.gross_cost,
              fixed_margin: dayData.fixed_margin,
              margin_percentage: dayData.margin_percentage
            });
          };

          // Process cost/margin data if provided
          if (rule.cost_margin_data) {
            const days: DayOfWeek[] = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
            const dayAbbrevs = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];

            // Extract default values from the first day's cost/margin data
            let defaultGrossCost = null;
            let defaultFixedMargin = null;
            let defaultMarginPercentage = null;

            for (let i = 0; i < days.length; i++) {
              const day = days[i];
              const abbrev = dayAbbrevs[i];
              const dayData = rule.cost_margin_data[abbrev];

              if (dayData) {
                // Store cost/margin fields for database
                costMarginFields[`${day}_gross_cost`] = dayData.gross_cost;
                costMarginFields[`${day}_fixed_margin`] = dayData.fixed_margin;
                costMarginFields[`${day}_margin_percentage`] = dayData.margin_percentage;

                // Use the first day's values as defaults (since all days have the same values in bulk mode)
                if (defaultGrossCost === null) {
                  defaultGrossCost = dayData.gross_cost;
                  defaultFixedMargin = dayData.fixed_margin;
                  defaultMarginPercentage = dayData.margin_percentage;
                }

                // Calculate price from cost/margin
                const calculatedPrice = calculatePriceFromCostMargin(dayData);
                if (calculatedPrice !== null) {
                  calculatedPrices[`${day}_price`] = calculatedPrice;
                }
              }
            }

            // Store the extracted default values
            if (defaultGrossCost !== null) {
              console.log(`[BULK API] Extracted default values from cost_margin_data:`, {
                gross_cost: defaultGrossCost,
                fixed_margin: defaultFixedMargin,
                margin_percentage: defaultMarginPercentage
              });
              costMarginFields.default_gross_cost = defaultGrossCost;
              costMarginFields.default_fixed_margin = defaultFixedMargin;
              costMarginFields.default_margin_percentage = defaultMarginPercentage;

              // Calculate and store default total
              const defaultTotal = calculateTotalFromCostMargin({
                gross_cost: defaultGrossCost,
                fixed_margin: defaultFixedMargin,
                margin_percentage: defaultMarginPercentage
              });
              if (defaultTotal !== null) {
                console.log(`[BULK API] Calculated default_total from cost_margin_data: ${defaultTotal}`);
                costMarginFields.default_total = defaultTotal;
              }
            }

            // Set amount to Monday's calculated price or first available calculated price
            amount = calculatedPrices.monday_price ||
                     Object.values(calculatedPrices)[0] ||
                     (rule.weekday_prices?.mon) ||
                     0;
          } else if (rule.weekday_values && rule.weekday_prices) {
            // PRIORITY: Weekday-specific cost and margin mode (process weekday_values first)
            console.log(`[BULK API] Processing rule.weekday_values (weekday-specific structure):`, rule.weekday_values);
            // Process default values if provided
            if (rule.default_values) {
              console.log(`[BULK API] Also processing rule.default_values:`, rule.default_values);
              // Convert CHF to cents (multiply by 100)
              costMarginFields.default_gross_cost = rule.default_values.gross_cost ? Math.round(rule.default_values.gross_cost * 100) : null;
              costMarginFields.default_fixed_margin = rule.default_values.fixed_margin ? Math.round(rule.default_values.fixed_margin * 100) : null;
              costMarginFields.default_margin_percentage = rule.default_values.margin_percentage;

              // Store default total - prioritize provided value, then calculate if needed
              if (rule.default_values.total !== undefined && rule.default_values.total !== null) {
                console.log(`[BULK API] Using provided rule default_total: ${rule.default_values.total}`);
                // Convert CHF to cents
                costMarginFields.default_total = Math.round(rule.default_values.total * 100);
              } else if (rule.default_values.gross_cost) {
                // Calculate default total if not provided but cost data is available
                const defaultTotal = calculateTotalFromCostMargin({
                  gross_cost: rule.default_values.gross_cost,
                  fixed_margin: rule.default_values.fixed_margin,
                  margin_percentage: rule.default_values.margin_percentage
                });
                if (defaultTotal !== null) {
                  console.log(`[BULK API] Calculated rule default_total: ${defaultTotal}`);
                  // Convert CHF to cents
                  costMarginFields.default_total = Math.round(defaultTotal * 100);
                }
              }
            }

            // Process weekday-specific values
            console.log(`[BULK API] Processing weekday_values for cost/margin data...`);

            // Helper function to process weekday values consistently
            const processWeekdayValue = (dayData: any, dayName: string, fallbackPrice: number) => {
              if (!dayData) return {};

              console.log(`[BULK API] DEBUG - ${dayName} values:`, dayData);

              // FIXED: Based on user clarification, backend handles currency conversion
              // Frontend sends values in display units (CHF), backend converts to cents for storage
              const conversionFactor = 100; // Always convert CHF to cents for database storage

              console.log(`[BULK API] DEBUG - ${dayName} using CHF to cents conversion, factor: ${conversionFactor}`);

              const result: any = {};

              // Store cost/margin values (convert CHF to cents for database storage)
              // CRITICAL: Handle zero values explicitly to ensure they're stored correctly
              const grossCostValue = dayData.gross_cost !== null && dayData.gross_cost !== undefined ? Math.round(dayData.gross_cost * conversionFactor) : null;
              const fixedMarginValue = dayData.fixed_margin !== null && dayData.fixed_margin !== undefined ? Math.round(dayData.fixed_margin * conversionFactor) : null;
              const marginPercentageValue = dayData.margin_percentage !== null && dayData.margin_percentage !== undefined ? dayData.margin_percentage : null;

              result[`${dayName.toLowerCase()}_gross_cost`] = grossCostValue;
              result[`${dayName.toLowerCase()}_fixed_margin`] = fixedMarginValue;
              result[`${dayName.toLowerCase()}_margin_percentage`] = marginPercentageValue;

              console.log(`[BULK API] 🔍 ${dayName} cost/margin storage values:`, {
                original: { gross_cost: dayData.gross_cost, fixed_margin: dayData.fixed_margin, margin_percentage: dayData.margin_percentage },
                converted: { gross_cost: grossCostValue, fixed_margin: fixedMarginValue, margin_percentage: marginPercentageValue },
                fieldNames: [`${dayName.toLowerCase()}_gross_cost`, `${dayName.toLowerCase()}_fixed_margin`, `${dayName.toLowerCase()}_margin_percentage`]
              });

              // ARCHITECTURE: Always use provided weekday_prices for price field
              // Cost/margin data is stored separately and doesn't override explicit prices
              result[`${dayName.toLowerCase()}_price`] = Math.round(fallbackPrice * conversionFactor);
              console.log(`[BULK API] � ${dayName} using provided weekday price: ${fallbackPrice} CHF = ${fallbackPrice * conversionFactor} cents`);

              console.log(`[BULK API] 📊 ${dayName} FINAL DATABASE VALUES:`, result);

              // Verify that zero values are being set correctly
              Object.keys(result).forEach(key => {
                if (key.includes('gross_cost') || key.includes('fixed_margin') || key.includes('margin_percentage')) {
                  console.log(`[BULK API] 🎯 ${key}: ${result[key]} (type: ${typeof result[key]})`);
                }
              });

              return result;
            };

            // Process all weekday values using the helper function
            console.log(`[BULK API] 🔄 Processing weekday values...`);
            const mondayResult = processWeekdayValue(rule.weekday_values.mon, 'Monday', rule.weekday_prices.mon);
            console.log(`[BULK API] 📊 Monday processWeekdayValue result:`, mondayResult);
            Object.assign(costMarginFields, mondayResult);

            Object.assign(costMarginFields, processWeekdayValue(rule.weekday_values.tue, 'Tuesday', rule.weekday_prices.tue));
            Object.assign(costMarginFields, processWeekdayValue(rule.weekday_values.wed, 'Wednesday', rule.weekday_prices.wed));
            Object.assign(costMarginFields, processWeekdayValue(rule.weekday_values.thu, 'Thursday', rule.weekday_prices.thu));
            Object.assign(costMarginFields, processWeekdayValue(rule.weekday_values.fri, 'Friday', rule.weekday_prices.fri));
            Object.assign(costMarginFields, processWeekdayValue(rule.weekday_values.sat, 'Saturday', rule.weekday_prices.sat));
            Object.assign(costMarginFields, processWeekdayValue(rule.weekday_values.sun, 'Sunday', rule.weekday_prices.sun));

            console.log(`[BULK API] 🔍 costMarginFields after processing all weekdays:`, Object.keys(costMarginFields));

            // Use Monday price for the amount field (required by database schema)
            amount = costMarginFields.monday_price || Math.round(rule.weekday_prices.mon * 100);
            console.log(`[BULK API] 📋 FINAL costMarginFields (weekday-specific structure):`, costMarginFields);

            // Debug: Show all cost/margin fields that will be stored
            const costMarginFieldsOnly = Object.keys(costMarginFields)
              .filter(key => key.includes('gross_cost') || key.includes('fixed_margin') || key.includes('margin_percentage'))
              .reduce((acc, key) => {
                acc[key] = costMarginFields[key];
                return acc;
              }, {});
            console.log(`[BULK API] 🎯 COST/MARGIN FIELDS TO STORE:`, costMarginFieldsOnly);
          } else if (rule.default_values && rule.weekday_prices) {
            // Fallback: New restructured mode with only default_values (no weekday_values)
            console.log(`[BULK API] Processing rule.default_values only (fallback mode):`, rule.default_values);
            // Convert CHF to cents (multiply by 100)
            costMarginFields.default_gross_cost = rule.default_values.gross_cost ? Math.round(rule.default_values.gross_cost * 100) : null;
            costMarginFields.default_fixed_margin = rule.default_values.fixed_margin ? Math.round(rule.default_values.fixed_margin * 100) : null;
            costMarginFields.default_margin_percentage = rule.default_values.margin_percentage;

            // Store default total - prioritize provided value, then calculate if needed
            if (rule.default_values.total !== undefined && rule.default_values.total !== null) {
              console.log(`[BULK API] Using provided rule default_total: ${rule.default_values.total}`);
              // Convert CHF to cents
              costMarginFields.default_total = Math.round(rule.default_values.total * 100);
            } else if (rule.default_values.gross_cost) {
              // Calculate default total if not provided but cost data is available
              const defaultTotal = calculateTotalFromCostMargin({
                gross_cost: rule.default_values.gross_cost,
                fixed_margin: rule.default_values.fixed_margin,
                margin_percentage: rule.default_values.margin_percentage
              });
              if (defaultTotal !== null) {
                console.log(`[BULK API] Calculated rule default_total: ${defaultTotal}`);
                // Convert CHF to cents
                costMarginFields.default_total = Math.round(defaultTotal * 100);
              }
            }

            // Use weekday prices for amount - convert to cents
            amount = Math.round(rule.weekday_prices.mon * 100);
            console.log(`[BULK API] Final costMarginFields (fallback mode):`, costMarginFields);
          } else if (rule.weekday_prices) {
            // Legacy mode: use provided weekday prices only - convert to cents
            amount = Math.round(rule.weekday_prices.mon * 100);
          } else {
            throw new Error("Either weekday_prices or cost_margin_data must be provided");
          }

          // Store default values if provided (fallback for legacy mode when cost_margin_data is not used)
          if (default_values && !rule.cost_margin_data && !rule.default_values) {
            console.log(`[BULK API] Processing default_values (legacy mode):`, default_values);
            // Convert CHF to cents (multiply by 100)
            costMarginFields.default_gross_cost = default_values.gross_cost ? Math.round(default_values.gross_cost * 100) : null;
            costMarginFields.default_fixed_margin = default_values.fixed_margin ? Math.round(default_values.fixed_margin * 100) : null;
            costMarginFields.default_margin_percentage = default_values.margin_percentage;

            // Store default total - prioritize provided value, then calculate if needed
            if (default_values.total !== undefined && default_values.total !== null) {
              // Use the total provided by the frontend - convert to cents
              console.log(`[BULK API] Using provided default_total: ${default_values.total}`);
              costMarginFields.default_total = Math.round(default_values.total * 100);
            } else if (default_values.gross_cost) {
              // Calculate default total if not provided but cost data is available
              const defaultTotal = calculateTotalFromCostMargin({
                gross_cost: default_values.gross_cost,
                fixed_margin: default_values.fixed_margin,
                margin_percentage: default_values.margin_percentage
              });
              if (defaultTotal !== null) {
                console.log(`[BULK API] Calculated default_total: ${defaultTotal}`);
                // Convert CHF to cents
                costMarginFields.default_total = Math.round(defaultTotal * 100);
              }
            }
            console.log(`[BULK API] Final costMarginFields (legacy mode):`, costMarginFields);
          }

          console.log(`[BULK API] Final costMarginFields for rule:`, costMarginFields);

          // Create a key to look up existing rules (include currency to distinguish between currencies)
          const key = `${rule.occupancy_type_id}_${rule.meal_plan_id || 'null'}_${currency_code}`;
          let existingRule = existingRulesMap.get(key);

          // If not found, try looking for legacy rules without currency (they default to USD)
          if (!existingRule && currency_code === 'USD') {
            const legacyKey = `${rule.occupancy_type_id}_${rule.meal_plan_id || 'null'}_USD`;
            existingRule = existingRulesMap.get(legacyKey);
          }

          if (existingRule) {
            // Update existing rule
            console.log(`Updating existing rule for occupancy type ${rule.occupancy_type_id} and meal plan ${rule.meal_plan_id || 'null'}`);

            // Check if there are actual changes to avoid unnecessary updates
            let hasChanges = existingRule.amount !== amount || existingRule.currency_code !== currency_code;

            console.log(`[BULK API] DEBUG - Initial hasChanges check: amount=${existingRule.amount} vs ${amount}, currency=${existingRule.currency_code} vs ${currency_code}, hasChanges=${hasChanges}`);

            // Check price changes (legacy or calculated)
            const newPrices = rule.weekday_prices || {};
            hasChanges = hasChanges ||
              existingRule.monday_price !== (calculatedPrices.monday_price || newPrices.mon) ||
              existingRule.tuesday_price !== (calculatedPrices.tuesday_price || newPrices.tue) ||
              existingRule.wednesday_price !== (calculatedPrices.wednesday_price || newPrices.wed) ||
              existingRule.thursday_price !== (calculatedPrices.thursday_price || newPrices.thu) ||
              existingRule.friday_price !== (calculatedPrices.friday_price || newPrices.fri) ||
              existingRule.saturday_price !== (calculatedPrices.saturday_price || newPrices.sat) ||
              existingRule.sunday_price !== (calculatedPrices.sunday_price || newPrices.sun);

            // Check cost and margin field changes
            console.log(`[BULK API] DEBUG - Checking cost/margin field changes for existing rule:`, existingRule.id);
            console.log(`[BULK API] DEBUG - costMarginFields to check:`, costMarginFields);
            for (const [field, value] of Object.entries(costMarginFields)) {
              console.log(`[BULK API] DEBUG - Checking field ${field}: existing=${existingRule[field]}, new=${value}`);
              if (existingRule[field] !== value) {
                console.log(`[BULK API] DEBUG - Field ${field} has changed, marking hasChanges=true`);
                hasChanges = true;
                break;
              }
            }
            console.log(`[BULK API] DEBUG - Final hasChanges value:`, hasChanges);

            // Force update if we have new default_total value (important for cost/margin updates)
            if (costMarginFields.default_total !== undefined && costMarginFields.default_total !== null) {
              console.log(`[BULK API] DEBUG - Forcing update due to default_total field:`, costMarginFields.default_total);
              hasChanges = true;
            }

            // Force update if we have weekday-specific cost/margin values
            if (rule.weekday_values) {
              console.log(`[BULK API] DEBUG - Forcing update due to weekday_values:`, rule.weekday_values);
              hasChanges = true;
            }

            if (hasChanges) {
              // Create a history entry for this update
              const historyEntry = {
                timestamp: new Date().toISOString(),
                action: "updated",
                previous_values: {
                  amount: existingRule.amount,
                  monday_price: existingRule.monday_price,
                  tuesday_price: existingRule.tuesday_price,
                  wednesday_price: existingRule.wednesday_price,
                  thursday_price: existingRule.thursday_price,
                  friday_price: existingRule.friday_price,
                  saturday_price: existingRule.saturday_price,
                  sunday_price: existingRule.sunday_price,
                  currency_code: existingRule.currency_code,
                  // Include existing cost/margin values
                  ...Object.keys(costMarginFields).reduce((acc, field) => {
                    acc[field] = existingRule[field];
                    return acc;
                  }, {})
                },
                new_values: {
                  amount: amount,
                  monday_price: calculatedPrices.monday_price || (rule.weekday_prices?.mon ? Math.round(rule.weekday_prices.mon * 100) : null),
                  tuesday_price: calculatedPrices.tuesday_price || (rule.weekday_prices?.tue ? Math.round(rule.weekday_prices.tue * 100) : null),
                  wednesday_price: calculatedPrices.wednesday_price || (rule.weekday_prices?.wed ? Math.round(rule.weekday_prices.wed * 100) : null),
                  thursday_price: calculatedPrices.thursday_price || (rule.weekday_prices?.thu ? Math.round(rule.weekday_prices.thu * 100) : null),
                  friday_price: calculatedPrices.friday_price || (rule.weekday_prices?.fri ? Math.round(rule.weekday_prices.fri * 100) : null),
                  saturday_price: calculatedPrices.saturday_price || (rule.weekday_prices?.sat ? Math.round(rule.weekday_prices.sat * 100) : null),
                  sunday_price: calculatedPrices.sunday_price || (rule.weekday_prices?.sun ? Math.round(rule.weekday_prices.sun * 100) : null),
                  currency_code,
                  // Include new cost/margin values
                  ...costMarginFields
                }
              };

              // Get existing history or create a new array
              const existingHistory = existingRule.metadata?.history || [];

              // Update the rule with new values
              const updateData = {
                id: existingRule.id,
                amount: amount,
                monday_price: calculatedPrices.monday_price || (rule.weekday_prices?.mon ? Math.round(rule.weekday_prices.mon * 100) : null),
                tuesday_price: calculatedPrices.tuesday_price || (rule.weekday_prices?.tue ? Math.round(rule.weekday_prices.tue * 100) : null),
                wednesday_price: calculatedPrices.wednesday_price || (rule.weekday_prices?.wed ? Math.round(rule.weekday_prices.wed * 100) : null),
                thursday_price: calculatedPrices.thursday_price || (rule.weekday_prices?.thu ? Math.round(rule.weekday_prices.thu * 100) : null),
                friday_price: calculatedPrices.friday_price || (rule.weekday_prices?.fri ? Math.round(rule.weekday_prices.fri * 100) : null),
                saturday_price: calculatedPrices.saturday_price || (rule.weekday_prices?.sat ? Math.round(rule.weekday_prices.sat * 100) : null),
                sunday_price: calculatedPrices.sunday_price || (rule.weekday_prices?.sun ? Math.round(rule.weekday_prices.sun * 100) : null),
                currency_code: validatedCurrencyCode,
                // Include all cost and margin fields
                ...costMarginFields,
                metadata: {
                  ...(existingRule.metadata || {}),
                  last_updated: new Date().toISOString(),
                  history: [...existingHistory, historyEntry]
                }
              };

              console.log(`[BULK API] 🚀 UPDATE DATA being sent to service:`, updateData);

              // Log specifically the cost/margin fields being updated
              const costMarginFieldsToUpdate = Object.keys(updateData).filter(key =>
                key.includes('gross_cost') || key.includes('fixed_margin') || key.includes('margin_percentage')
              );
              console.log(`[BULK API] 💾 Cost/Margin fields being updated:`,
                costMarginFieldsToUpdate.reduce((acc, key) => {
                  acc[key] = updateData[key];
                  return acc;
                }, {})
              );

              console.log(`[BULK API] 🔍 About to call updateBasePriceRules with:`, {
                id: updateData.id,
                weekdayPriceFields: {
                  monday_price: updateData.monday_price,
                  tuesday_price: updateData.tuesday_price,
                  wednesday_price: updateData.wednesday_price,
                  thursday_price: updateData.thursday_price,
                  friday_price: updateData.friday_price,
                  saturday_price: updateData.saturday_price,
                  sunday_price: updateData.sunday_price,
                }
              });

              const updatedRule = await hotelPricingService.updateBasePriceRules([updateData]);

              console.log(`[BULK API] ✅ Rule updated successfully:`, updatedRule[0]?.id);
              console.log(`[BULK API] 🔍 Updated rule weekday prices:`, {
                monday_price: updatedRule[0]?.monday_price,
                tuesday_price: updatedRule[0]?.tuesday_price,
                wednesday_price: updatedRule[0]?.wednesday_price,
                thursday_price: updatedRule[0]?.thursday_price,
                friday_price: updatedRule[0]?.friday_price,
                saturday_price: updatedRule[0]?.saturday_price,
                sunday_price: updatedRule[0]?.sunday_price,
              });

              createdRules.push(updatedRule[0]);
            } else {
              console.log(`No changes detected for rule ${existingRule.id}, skipping update`);
              createdRules.push(existingRule);
            }
          } else {
            // Create new rule
            console.log(`Creating new rule for occupancy type ${rule.occupancy_type_id} and meal plan ${rule.meal_plan_id || 'null'}`);

            // Create a new base price rule
            const newRule = {
              room_config_id: roomConfigId,
              hotel_id: hotelId,
              occupancy_type_id: rule.occupancy_type_id,
              meal_plan_id: rule.meal_plan_id,
              amount: amount, // Required field (already converted to cents)
              monday_price: calculatedPrices.monday_price || (rule.weekday_prices?.mon ? Math.round(rule.weekday_prices.mon * 100) : null),
              tuesday_price: calculatedPrices.tuesday_price || (rule.weekday_prices?.tue ? Math.round(rule.weekday_prices.tue * 100) : null),
              wednesday_price: calculatedPrices.wednesday_price || (rule.weekday_prices?.wed ? Math.round(rule.weekday_prices.wed * 100) : null),
              thursday_price: calculatedPrices.thursday_price || (rule.weekday_prices?.thu ? Math.round(rule.weekday_prices.thu * 100) : null),
              friday_price: calculatedPrices.friday_price || (rule.weekday_prices?.fri ? Math.round(rule.weekday_prices.fri * 100) : null),
              saturday_price: calculatedPrices.saturday_price || (rule.weekday_prices?.sat ? Math.round(rule.weekday_prices.sat * 100) : null),
              sunday_price: calculatedPrices.sunday_price || (rule.weekday_prices?.sun ? Math.round(rule.weekday_prices.sun * 100) : null),
              currency_code: validatedCurrencyCode,
              min_occupancy: 1, // Default value
              max_occupancy: null, // No upper limit
              // Include all cost and margin fields
              ...costMarginFields,
              metadata: {
                created_at: new Date().toISOString(),
                history: [{
                  timestamp: new Date().toISOString(),
                  action: "created",
                  values: {
                    amount: amount,
                    monday_price: calculatedPrices.monday_price || (rule.weekday_prices?.mon ? Math.round(rule.weekday_prices.mon * 100) : null),
                    tuesday_price: calculatedPrices.tuesday_price || (rule.weekday_prices?.tue ? Math.round(rule.weekday_prices.tue * 100) : null),
                    wednesday_price: calculatedPrices.wednesday_price || (rule.weekday_prices?.wed ? Math.round(rule.weekday_prices.wed * 100) : null),
                    thursday_price: calculatedPrices.thursday_price || (rule.weekday_prices?.thu ? Math.round(rule.weekday_prices.thu * 100) : null),
                    friday_price: calculatedPrices.friday_price || (rule.weekday_prices?.fri ? Math.round(rule.weekday_prices.fri * 100) : null),
                    saturday_price: calculatedPrices.saturday_price || (rule.weekday_prices?.sat ? Math.round(rule.weekday_prices.sat * 100) : null),
                    sunday_price: calculatedPrices.sunday_price || (rule.weekday_prices?.sun ? Math.round(rule.weekday_prices.sun * 100) : null),
                    currency_code: validatedCurrencyCode,
                    // Include cost and margin values in history
                    ...costMarginFields
                  }
                }]
              }
            };

            console.log(`[BULK API] 🆕 NEW RULE being sent to service:`, newRule);

            // Log specifically the cost/margin fields being created
            const costMarginFieldsToCreate = Object.keys(newRule).filter(key =>
              key.includes('gross_cost') || key.includes('fixed_margin') || key.includes('margin_percentage')
            );
            console.log(`[BULK API] 💾 Cost/Margin fields being created:`,
              costMarginFieldsToCreate.reduce((acc, key) => {
                acc[key] = newRule[key];
                return acc;
              }, {})
            );

            // The method expects an array (consistent with updateBasePriceRules)
            const createdRuleArray = await hotelPricingService.createBasePriceRules([newRule]);

            console.log(`[BULK API] ✅ Rule created successfully:`, createdRuleArray[0]?.id);

            createdRules.push(createdRuleArray[0]);
          }
        } catch (error) {
          console.error(`Error processing base price rule for occupancy type ${rule.occupancy_type_id} and meal plan ${rule.meal_plan_id}:`, error);
          // Continue with other rules
        }
      }
    } catch (error) {
      console.error("Error in bulk create operation:", error);
      return res.status(500).json({
        message: "An error occurred while bulk upserting weekday pricing rules",
        error: error instanceof Error ? error.message : String(error),
      });
    }

    // Format the response
    const formattedRules = await Promise.all(createdRules.map(async (rule) => {
      // Get the occupancy type
      const occupancyConfig = await hotelPricingService.retrieveOccupancyConfig(rule.occupancy_type_id);

      // Get the meal plan
      const mealPlan = rule.meal_plan_id ? await hotelPricingService.retrieveMealPlan(rule.meal_plan_id) : null;

      return {
        id: rule.id,
        occupancy_type_id: rule.occupancy_type_id,
        occupancy_type: occupancyConfig,
        meal_plan_id: rule.meal_plan_id,
        meal_plan: mealPlan,
        room_config_id: rule.room_config_id,
        weekday_prices: {
          mon: rule.monday_price !== null && rule.monday_price !== undefined ? rule.monday_price : rule.amount,
          tue: rule.tuesday_price !== null && rule.tuesday_price !== undefined ? rule.tuesday_price : rule.amount,
          wed: rule.wednesday_price !== null && rule.wednesday_price !== undefined ? rule.wednesday_price : rule.amount,
          thu: rule.thursday_price !== null && rule.thursday_price !== undefined ? rule.thursday_price : rule.amount,
          fri: rule.friday_price !== null && rule.friday_price !== undefined ? rule.friday_price : rule.amount,
          sat: rule.saturday_price !== null && rule.saturday_price !== undefined ? rule.saturday_price : rule.amount,
          sun: rule.sunday_price !== null && rule.sunday_price !== undefined ? rule.sunday_price : rule.amount,
        },
        currency_code: rule.currency_code,
        created_at: rule.created_at,
        updated_at: rule.updated_at,
      };
    }));

    res.status(201).json({
      weekday_rules: formattedRules,
    });
  } catch (error) {
    console.error("Error bulk upserting weekday pricing rules:", error);
    res.status(500).json({
      message: "An error occurred while bulk upserting weekday pricing rules",
      error: error instanceof Error ? error.message : String(error),
    });
  }
};

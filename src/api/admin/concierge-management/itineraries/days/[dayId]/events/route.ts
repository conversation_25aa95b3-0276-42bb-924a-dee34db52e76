import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import ItineraryService from "../../../../../../../modules/concierge-management/itinerary-service";

// Validation schemas
export const PostAddItineraryEvent = z.object({
  category: z.enum(["Flight", "Lodging", "Activity", "Cruise", "Transport", "Info"]),
  type: z.string().optional(),
  title: z.string(),
  notes: z.string().optional(),
  start_time: z.string().optional(), // HH:MM format
  end_time: z.string().optional(),
  duration: z.string().optional(),
  timezone: z.string().optional(),
  details: z.record(z.any()).optional(),
  price: z.number().optional(),
  currency: z.string().optional(),
  media: z.array(z.string()).optional(),
  attachments: z.array(z.string()).optional(),
  people: z.array(z.string()).optional(),
});

export type PostAddItineraryEventType = z.infer<typeof PostAddItineraryEvent>;

/**
 * POST /admin/concierge-management/itineraries/days/:dayId/events
 * Add a new event to a day
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { dayId } = req.params;
    const validatedData = PostAddItineraryEvent.parse(req.body);
    
    const itineraryService: ItineraryService = req.scope.resolve("itineraryService");
    
    const event = await itineraryService.addEventToDay(dayId, validatedData);
    
    res.status(201).json({
      event,
      message: "Event added successfully",
    });
  } catch (error) {
    console.error("Error adding event to day:", error);
    res.status(400).json({
      error: error instanceof Error ? error.message : "Failed to add event to day",
    });
  }
}

/**
 * GET /admin/concierge-management/itineraries/days/:dayId/events
 * Get all events for a specific day
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { dayId } = req.params;
    
    const itineraryService: ItineraryService = req.scope.resolve("itineraryService");
    
    const events = await itineraryService.getEventsForDay(dayId);
    
    res.json({
      events,
      count: events.length,
    });
  } catch (error) {
    console.error("Error getting events for day:", error);
    res.status(400).json({
      error: error instanceof Error ? error.message : "Failed to get events for day",
    });
  }
}

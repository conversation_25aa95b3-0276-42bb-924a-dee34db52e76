import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework";
import { z } from "zod";
import CustomerTravellerModuleService from "../../../../../modules/customer-travellers/service";
import { CUSTOMER_TRAVELLER_MODULE } from "../../../../../modules/customer-travellers";
import { Gender, Relationship } from "../../../../../modules/customer-travellers/types";

// Validation schemas
const CreateTravellerSchema = z.object({
  first_name: z.string().min(1, "First name is required"),
  last_name: z.string().min(1, "Last name is required"),
  gender: z.nativeEnum(Gender),
  date_of_birth: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format"),
  relationship: z.nativeEnum(Relationship).optional(),
});

const UpdateTravellerSchema = z.object({
  first_name: z.string().min(1, "First name is required").optional(),
  last_name: z.string().min(1, "Last name is required").optional(),
  gender: z.nativeEnum(Gender).optional(),
  date_of_birth: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format").optional(),
  relationship: z.nativeEnum(Relationship).optional(),
});

type CreateTravellerInput = z.infer<typeof CreateTravellerSchema>;
type UpdateTravellerInput = z.infer<typeof UpdateTravellerSchema>;

/**
 * GET /store/customers/me/travellers
 * List all travellers for the authenticated customer
 */
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const customerTravellerService: CustomerTravellerModuleService =
      req.scope.resolve(CUSTOMER_TRAVELLER_MODULE);

    const customerId = req.auth_context.actor_id;
    const travellers = await customerTravellerService.getCustomerTravellers(customerId);

    res.json({
      travellers,
      count: travellers.length,
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      message: error instanceof Error ? error.message : "Failed to fetch travellers",
    });
  }
};

/**
 * POST /store/customers/me/travellers
 * Create a new traveller for the authenticated customer
 */
export const POST = async (
  req: AuthenticatedMedusaRequest<CreateTravellerInput>,
  res: MedusaResponse
) => {
  try {
    const validatedData = CreateTravellerSchema.parse(req.body);
    
    const customerTravellerService: CustomerTravellerModuleService =
      req.scope.resolve(CUSTOMER_TRAVELLER_MODULE);

    const customerId = req.auth_context.actor_id;
    const traveller = await customerTravellerService.createCustomerTraveller({
      customer_id: customerId,
      ...validatedData,
    });

    res.status(201).json({
      traveller,
      success: true,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      res.status(400).json({
        success: false,
        message: "Validation error",
        errors: error.errors,
      });
    } else {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : "Failed to create traveller",
      });
    }
  }
};

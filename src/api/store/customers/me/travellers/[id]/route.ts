import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework";
import { z } from "zod";
import CustomerTravellerModuleService from "../../../../../../modules/customer-travellers/service";
import { CUSTOMER_TRAVELLER_MODULE } from "../../../../../../modules/customer-travellers";
import { Gender, Relationship } from "../../../../../../modules/customer-travellers/types";

// Validation schemas
const UpdateTravellerSchema = z.object({
  first_name: z.string().min(1, "First name is required").optional(),
  last_name: z.string().min(1, "Last name is required").optional(),
  gender: z.nativeEnum(Gender).optional(),
  date_of_birth: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format").optional(),
  relationship: z.nativeEnum(Relationship).optional(),
});

type UpdateTravellerInput = z.infer<typeof UpdateTravellerSchema>;

/**
 * GET /store/customers/me/travellers/:id
 * Get a specific traveller for the authenticated customer
 */
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const customerTravellerService: CustomerTravellerModuleService =
      req.scope.resolve(CUSTOMER_TRAVELLER_MODULE);

    const customerId = req.auth_context.actor_id;
    const travellerId = req.params.id;

    const traveller = await customerTravellerService.getCustomerTraveller(
      travellerId,
      customerId
    );

    res.json({
      traveller,
      success: true,
    });
  } catch (error) {
    res.status(404).json({
      success: false,
      message: error instanceof Error ? error.message : "Traveller not found",
    });
  }
};

/**
 * PUT /store/customers/me/travellers/:id
 * Update a specific traveller for the authenticated customer
 */
export const PUT = async (
  req: AuthenticatedMedusaRequest<UpdateTravellerInput>,
  res: MedusaResponse
) => {
  try {
    const validatedData = UpdateTravellerSchema.parse(req.body);
    
    const customerTravellerService: CustomerTravellerModuleService =
      req.scope.resolve(CUSTOMER_TRAVELLER_MODULE);

    const customerId = req.auth_context.actor_id;
    const travellerId = req.params.id;

    const traveller = await customerTravellerService.updateCustomerTraveller(
      travellerId,
      customerId,
      validatedData
    );

    res.json({
      traveller,
      success: true,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      res.status(400).json({
        success: false,
        message: "Validation error",
        errors: error.errors,
      });
    } else {
      res.status(404).json({
        success: false,
        message: error instanceof Error ? error.message : "Failed to update traveller",
      });
    }
  }
};

/**
 * DELETE /store/customers/me/travellers/:id
 * Delete a specific traveller for the authenticated customer
 */
export const DELETE = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const customerTravellerService: CustomerTravellerModuleService =
      req.scope.resolve(CUSTOMER_TRAVELLER_MODULE);

    const customerId = req.auth_context.actor_id;
    const travellerId = req.params.id;

    await customerTravellerService.deleteCustomerTraveller(
      travellerId,
      customerId
    );

    res.json({
      success: true,
      message: "Traveller deleted successfully",
    });
  } catch (error) {
    res.status(404).json({
      success: false,
      message: error instanceof Error ? error.message : "Failed to delete traveller",
    });
  }
};

import { defineWidgetConfig } from "@camped-ai/admin-sdk";
import { useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useRbac } from "../hooks/use-rbac";
import {
  HIDDEN_SIDEBAR_ITEMS,
  EXCEPTION_ITEMS,
} from "../config/sidebar-config";

/**
 * Permission-based sidebar hider that hides menu items based on RBAC permissions
 * Combines static hiding (from config) with dynamic permission-based hiding
 */
// Immediately inject CSS to prevent flash of unstyled content
const injectImmediateHidingCSS = () => {
  const styleId = "immediate-sidebar-hiding";

  if (!document.getElementById(styleId)) {
    const style = document.createElement("style");
    style.id = styleId;
    style.textContent = `
      /* Immediately hide all sidebar navigation until permissions are loaded */
      nav, aside, [role="navigation"], [data-testid="sidebar"] {
        opacity: 0 !important;
        transition: opacity 0.3s ease !important;
      }

      /* Show sidebar only when explicitly marked as ready */
      nav[data-permissions-ready="true"],
      aside[data-permissions-ready="true"],
      [role="navigation"][data-permissions-ready="true"],
      [data-testid="sidebar"][data-permissions-ready="true"] {
        opacity: 1 !important;
      }
    `;
    document.head.appendChild(style);
  }
};

// Run immediately when module loads
injectImmediateHidingCSS();

const PermissionBasedSidebarHider = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { hasPermission, isAdmin, currentUser, loading } = useRbac();

  // Inject CSS to hide sidebar initially
  useEffect(() => {
    const styleId = "permission-sidebar-loading-style";

    if (!document.getElementById(styleId)) {
      const style = document.createElement("style");
      style.id = styleId;
      style.textContent = `
        /* Hide sidebar navigation while permissions are loading */
        nav[data-loading="true"],
        aside[data-loading="true"],
        [role="navigation"][data-loading="true"],
        [data-testid="sidebar"][data-loading="true"] {
          opacity: 0 !important;
          pointer-events: none !important;
          transition: opacity 0.3s ease !important;
        }

        /* Show sidebar after permissions are loaded */
        nav[data-loading="false"],
        aside[data-loading="false"],
        [role="navigation"][data-loading="false"],
        [data-testid="sidebar"][data-loading="false"] {
          opacity: 1 !important;
          pointer-events: auto !important;
          transition: opacity 0.3s ease !important;
        }
      `;
      document.head.appendChild(style);
    }

    // Set loading state on sidebar elements
    const sidebarElements = document.querySelectorAll(
      'nav, aside, [role="navigation"], [data-testid="sidebar"]'
    );

    sidebarElements.forEach((sidebar) => {
      (sidebar as HTMLElement).setAttribute("data-loading", loading.toString());
    });

    return () => {
      // Cleanup: remove loading state when component unmounts
      sidebarElements.forEach((sidebar) => {
        (sidebar as HTMLElement).removeAttribute("data-loading");
      });
    };
  }, [loading]);

  useEffect(() => {
    // Hide sidebar immediately while RBAC is loading
    const hideSidebarWhileLoading = () => {
      const sidebarElements = document.querySelectorAll(
        'nav, aside, [role="navigation"], [data-testid="sidebar"]'
      );

      sidebarElements.forEach((sidebar) => {
        if (loading) {
          (sidebar as HTMLElement).style.opacity = "0";
          (sidebar as HTMLElement).style.pointerEvents = "none";
        } else {
          (sidebar as HTMLElement).style.opacity = "1";
          (sidebar as HTMLElement).style.pointerEvents = "auto";
        }
      });
    };

    // Always run this to handle loading state
    hideSidebarWhileLoading();

    // Don't run permission hiding if RBAC is still loading
    if (loading) return;

    let hasRun = false;

    // Handle redirections for unauthorized access
    const handleRedirection = () => {
      const currentPath = location.pathname;

      // Redirect unauthorized users away from admin-only pages
      if (currentPath.includes("/admin/settings") && !isAdmin()) {
        navigate("/");
        return;
      }
    };

    // Run redirection check first
    handleRedirection();

    // Immediate search hiding function
    const hideSearchImmediately = () => {
      const searchElements = document.querySelectorAll(
        'nav *, aside *, [role="navigation"] *, [data-testid="sidebar"] *'
      );

      searchElements.forEach((element) => {
        const text = element.textContent?.trim() || "";
        if (text.toLowerCase() === "search") {
          const listItem = element.closest("li");
          const targetElement = listItem || element.parentElement || element;
          (targetElement as HTMLElement).style.display = "none";
        }
      });
    };

    // Hide search immediately
    hideSearchImmediately();

    const hideSidebarItems = () => {
      if (hasRun) return;
      hasRun = true;

      // Define permission mappings for sidebar items based on actual RBAC permissions
      const permissionMappings = {
        // Hotel Management
        "hotel management": "hotel_management:view",
        hotels: "hotel_management:view",

        // Destinations
        destinations: "destinations:view",
        destination: "destinations:view",

        // Room Management
        "room management": "rooms:view",
        rooms: "rooms:view",
        "room configs": "rooms:view",
        "room configurations": "rooms:view",
        availability: "rooms:availability",

        // Bookings
        bookings: "bookings:view",
        booking: "bookings:view",

        // Add-on Services
        "add-on services": "addon_services:view",

        // Add-ons Inventory
        "add ons": "addons:view",
        "add-ons": "addons:view",

        // Inventory Management
        inventory: "addons:view", // Using addons permission for inventory access
        "inventory management": "addons:view",

        // User Management
        "user management": "user_management:view",
        users: "user_management:view",

        // Role Management
        "role management": "role_management:view",
        roles: "role_management:view",
        "users & roles": "role_management:view",

        // Analytics
        analytics: "analytics:view",
        "store analytics": "analytics:store",
        "user analytics": "analytics:user",

        // Reports
        reports: "reports:view",
        "hotel reports": "reports:hotel",

        // Supplier Management
        "supplier management": "supplier_management:view",
        suppliers: "supplier_management:view",
        "products-services": "supplier_management:view",
        "products & services": "supplier_management:view",
        "supplier products": "supplier_management:view",
        contracts: "supplier_management:view",
        "supplier contracts": "supplier_management:view",
        orders: "supplier_management:view",
        "supplier orders": "supplier_management:view",

        // Vendor Management (Legacy)
        "vendor management": "vendor_management:view",
        vendors: "vendor_management:view",

        // Fulfillment Operations
        "fulfillment operations": "fulfillment_operations:view",
        itineraries: "fulfillment_operations:view",
        tasks: "fulfillment_operations:view",

        // Storefront
        storefront: "storefront:view",

        // Carts
        carts: "carts:view",
        cart: "carts:view",

        // Subscriptions
        subscriptions: "subscriptions:view",
        subscription: "subscriptions:view",

        // Pricing
        pricing: "pricing:view",
        "hotel pricing": "pricing:view",
        price: "pricing:view",
        rates: "pricing:view",

        // User Manual
        "user manual": "user_manual:view",

        // Settings - specific items that require admin or specific permissions
        "api keys": "admin_only",
        "publishable api keys": "admin_only",
        "secret api keys": "admin_only",
        webhooks: "admin_only",
        workflows: "admin_only",
        locations: "admin_only",
        shipping: "admin_only",
        tax: "admin_only",
        regions: "admin_only",
        currencies: "admin_only",
        "return reasons": "admin_only",
        "sales channels": "admin_only",
        "product types": "admin_only",
        "product tags": "admin_only",

        // Specific settings permissions
        "notification templates": "settings:notification_templates",
        "add-on categories": "settings:addon_categories",
        "bulk import": "settings:bulk_import",
        settings: "settings:view", // General settings access

        // Items to always hide
        search: "never_show", // Hide search from everyone including admins

        // User profile items that should always be visible to all authenticated users
        profile: "always_show",
        "profile settings": "always_show",
        "change password": "always_show",
        password: "always_show",
        account: "always_show",
        "account settings": "always_show",

        // Concierge Management
        "concierge management": "concierge_management:view",
        concierge: "concierge_management:view",
        tasks: "tasks:view",
        itineraries: "itineraries:view",
        bookings: "bookings:view", // Added general bookings mapping
        "concierge bookings": "bookings:view",
        "concierge tasks": "tasks:view",
        "concierge itineraries": "itineraries:view",
        "status configuration": "concierge_config:manage",
        "email templates": "concierge_config:manage",
      };

      // Get static hidden items (normalized)
      const staticHiddenItems = HIDDEN_SIDEBAR_ITEMS.map((item) =>
        item.toLowerCase()
      );

      // Get exception items (normalized)
      const exceptionItems = EXCEPTION_ITEMS.map((item) => item.toLowerCase());

      let hiddenCount = 0;

      // Target navigation links
      const navLinks = document.querySelectorAll(
        'nav a, aside a, [role="navigation"] a, [data-testid="sidebar"] a'
      );

      navLinks.forEach((link) => {
        const text = link.textContent?.trim() || "";
        const normalizedText = text.toLowerCase();
        const href = (link as HTMLAnchorElement).href || "";

        // Skip if empty text
        if (!text) return;

        // Check for user profile and password related items that should always be visible
        const isUserProfileItem =
          normalizedText.includes("profile") ||
          normalizedText.includes("change password") ||
          normalizedText.includes("password") ||
          href.includes("/settings/profile") ||
          href.includes("/app/settings/profile") ||
          href.includes("/settings/change-password") ||
          href.includes("/app/settings/change-password") ||
          (normalizedText.includes("profile") &&
            normalizedText.includes("settings"));

        // Check static hiding first (from config)
        // But allow supplier management sub-items, role management items, and user profile items even if they match static hidden items
        const isSupplierManagementSubItem =
          href.includes("/supplier-management/") ||
          normalizedText.includes("supplier") ||
          (normalizedText.includes("products") &&
            normalizedText.includes("services")) ||
          (normalizedText.includes("orders") &&
            href.includes("/supplier-management/")) ||
          (normalizedText.includes("contracts") &&
            href.includes("/supplier-management/"));

        const isRoleManagementItem =
          href.includes("/app/settings/roles") ||
          href.includes("/settings/roles") ||
          normalizedText.includes("users & roles") ||
          normalizedText.includes("role management") ||
          (normalizedText.includes("users") &&
            normalizedText.includes("roles"));

        // Check if this item is in the exception list (should not be hidden)
        const isExceptionItem = exceptionItems.some((exception) => {
          return (
            normalizedText === exception || normalizedText.includes(exception)
          );
        });

        const shouldHideStatic =
          !isSupplierManagementSubItem &&
          !isRoleManagementItem &&
          !isUserProfileItem &&
          !isExceptionItem && // Don't hide if it's an exception item
          staticHiddenItems.some((item) => {
            return normalizedText === item || normalizedText.includes(item);
          });

        // Check permission-based hiding
        let shouldHidePermission = false;

        // Never hide user profile items regardless of permissions
        if (isUserProfileItem) {
          shouldHidePermission = false;
        } else {
          // Only check permissions for non-admin users
          if (!isAdmin() && currentUser?.rbac) {
            // Check if this menu item requires a specific permission
            const requiredPermission = Object.entries(permissionMappings).find(
              ([key]) => {
                return normalizedText.includes(key.toLowerCase());
              }
            )?.[1];

            if (requiredPermission) {
              if (requiredPermission === "never_show") {
                // Hide from everyone including admins
                shouldHidePermission = true;
              } else if (requiredPermission === "always_show") {
                // Always show these items to all authenticated users
                shouldHidePermission = false;
              } else if (requiredPermission === "admin_only") {
                // Hide admin-only items for non-admin users
                shouldHidePermission = true;
              } else {
                // Check specific permission
                shouldHidePermission = !hasPermission(requiredPermission);
              }
            }
          } else if (isAdmin()) {
            // Admin users can see everything except never_show items
            const requiredPermission = Object.entries(permissionMappings).find(
              ([key]) => {
                return normalizedText.includes(key.toLowerCase());
              }
            )?.[1];

            if (requiredPermission === "never_show") {
              shouldHidePermission = true;
            } else if (requiredPermission === "always_show") {
              // Always show these items to all authenticated users including admins
              shouldHidePermission = false;
            } else {
              shouldHidePermission = false;
            }
          }
        }

        // Hide if either static config or permission check says to hide
        if (shouldHideStatic || shouldHidePermission) {
          const listItem = link.closest("li");
          const targetElement = listItem || link;
          (targetElement as HTMLElement).style.display = "none";
          hiddenCount++;
        } else if (isSupplierManagementSubItem) {
          // Supplier management sub-item allowed
        } else if (isRoleManagementItem) {
          // Ensure the element is visible
          const listItem = link.closest("li");
          const targetElement = listItem || link;
          (targetElement as HTMLElement).style.display = "block";
          (targetElement as HTMLElement).style.visibility = "visible";
        } else if (isUserProfileItem) {
          // Ensure user profile items are always visible
          const listItem = link.closest("li");
          const targetElement = listItem || link;
          (targetElement as HTMLElement).style.display = "block";
          (targetElement as HTMLElement).style.visibility = "visible";
        }
      });

      // Special handling for search - look for elements containing "Search" text
      const allElements = document.querySelectorAll(
        'nav *, aside *, [role="navigation"] *, [data-testid="sidebar"] *'
      );

      allElements.forEach((element) => {
        const text = element.textContent?.trim() || "";
        const normalizedText = text.toLowerCase();

        // Only target elements that contain exactly "search" and are likely navigation items
        if (
          normalizedText === "search" &&
          (element.tagName === "SPAN" ||
            element.tagName === "DIV" ||
            element.tagName === "A" ||
            element.tagName === "BUTTON")
        ) {
          const listItem = element.closest("li");
          const targetElement = listItem || element.parentElement || element;

          (targetElement as HTMLElement).style.display = "none";
          hiddenCount++;
        }
      });

      // Handle duplicate "Supplier Management" items - keep only the first one
      const supplierMgmtLinks = document.querySelectorAll(
        'nav a, aside a, [role="navigation"] a'
      );
      let supplierMgmtCount = 0;

      supplierMgmtLinks.forEach((link) => {
        const text = link.textContent?.trim() || "";
        if (text === "Supplier Management") {
          supplierMgmtCount++;
          if (supplierMgmtCount > 1) {
            const listItem = link.closest("li");
            const targetElement = listItem || link;
            (targetElement as HTMLElement).style.display = "none";
            hiddenCount++;
          }
        }
      });

      // Mark sidebar as ready to show after permission filtering
      const sidebarElements = document.querySelectorAll(
        'nav, aside, [role="navigation"], [data-testid="sidebar"]'
      );

      sidebarElements.forEach((sidebar) => {
        (sidebar as HTMLElement).setAttribute("data-permissions-ready", "true");
      });
    };

    // Run the hiding logic
    hideSidebarItems();

    // Run once more after a short delay to catch late-loading content
    const timeoutId = setTimeout(hideSidebarItems, 1000);

    // Fallback: ensure sidebar shows after maximum wait time
    const fallbackTimeoutId = setTimeout(() => {
      const sidebarElements = document.querySelectorAll(
        'nav, aside, [role="navigation"], [data-testid="sidebar"]'
      );

      sidebarElements.forEach((sidebar) => {
        if (!(sidebar as HTMLElement).hasAttribute("data-permissions-ready")) {
          (sidebar as HTMLElement).setAttribute(
            "data-permissions-ready",
            "true"
          );
        }
      });
    }, 3000); // 3 second fallback

    // Set up MutationObserver to handle dynamically loaded content
    const observer = new MutationObserver((mutations) => {
      let shouldRerun = false;
      mutations.forEach((mutation) => {
        if (mutation.type === "childList" && mutation.addedNodes.length > 0) {
          // Check if any added nodes contain navigation elements
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              if (
                element.querySelector(
                  'nav, aside, [role="navigation"], [data-testid="sidebar"]'
                ) ||
                element.matches(
                  'nav, aside, [role="navigation"], [data-testid="sidebar"]'
                )
              ) {
                shouldRerun = true;
              }
            }
          });
        }
      });

      if (shouldRerun) {
        setTimeout(hideSidebarItems, 100); // Small delay to let DOM settle
      }
    });

    // Observe changes to the document body
    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    // Cleanup
    return () => {
      clearTimeout(timeoutId);
      clearTimeout(fallbackTimeoutId);
      observer.disconnect();
    };
  }, [
    navigate,
    location.pathname,
    hasPermission,
    isAdmin,
    currentUser,
    loading,
  ]);

  return null;
};

export const config = defineWidgetConfig({
  zone: [
    "product.list.before",
    "order.list.before",
    "customer.list.before",
    "store.details.before",
    "inventory_item.list.before",
    "promotion.list.before",
    "price_list.list.before",
  ],
});

export default PermissionBasedSidebarHider;

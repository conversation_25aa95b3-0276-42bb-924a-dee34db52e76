import React, { useState, useEffect } from "react";
import {
  Button,
  Input,
  Select,
  Textarea,
  Text,
  Badge,
  Heading,
  Container,
} from "@camped-ai/ui";
import { Clock, MapPin, DollarSign, Users, Calendar, Info } from "lucide-react";
import MediaUpload from "./media-upload";
import TimePicker from "./time-picker";

interface EventFormProps {
  initialData?: any;
  onSubmit: (data: any) => void;
  onCancel: () => void;
}

interface EventFormData {
  category: string;
  type: string;
  title: string;
  notes: string;
  start_time: string;
  end_time: string;
  duration: string;
  timezone: string;
  details: Record<string, any>;
  price: number | null;
  currency: string;
  media: string[];
  attachments: string[];
  people: string[];
}

const EventForm: React.FC<EventFormProps> = ({ initialData, onSubmit, onCancel }) => {
  const [formData, setFormData] = useState<EventFormData>({
    category: "Activity",
    type: "",
    title: "",
    notes: "",
    start_time: "",
    end_time: "",
    duration: "",
    timezone: "",
    details: {},
    price: null,
    currency: "USD",
    media: [],
    attachments: [],
    people: [],
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form with existing data or template data
  useEffect(() => {
    if (initialData) {
      setFormData({
        category: initialData.category || "Activity",
        type: initialData.type || "",
        title: initialData.title || "",
        notes: initialData.notes || "",
        start_time: initialData.start_time || "",
        end_time: initialData.end_time || "",
        duration: initialData.duration || "",
        timezone: initialData.timezone || "",
        details: initialData.details || {},
        price: initialData.price || null,
        currency: initialData.currency || "USD",
        media: initialData.media || [],
        attachments: initialData.attachments || [],
        people: initialData.people || [],
      });
    }
  }, [initialData]);

  // Validation functions
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Required fields
    if (!formData.title.trim()) {
      newErrors.title = "Event title is required";
    }

    if (!formData.category) {
      newErrors.category = "Category is required";
    }

    // Time validation
    if (formData.start_time && formData.end_time) {
      const start = new Date(`2000-01-01T${formData.start_time}`);
      const end = new Date(`2000-01-01T${formData.end_time}`);

      if (end <= start) {
        newErrors.end_time = "End time must be after start time";
      }
    }

    // Price validation
    if (formData.price !== null && formData.price < 0) {
      newErrors.price = "Price cannot be negative";
    }

    // Category-specific validation
    if (formData.category === "Flight") {
      if (formData.details.flight_number && !/^[A-Z]{2,3}\d{1,4}$/i.test(formData.details.flight_number)) {
        newErrors.flight_number = "Invalid flight number format (e.g., AA123)";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Clean up data before submission
      const submitData = {
        ...formData,
        price: formData.price || undefined,
        type: formData.type || undefined,
        notes: formData.notes || undefined,
        start_time: formData.start_time || undefined,
        end_time: formData.end_time || undefined,
        duration: formData.duration || undefined,
        timezone: formData.timezone || undefined,
        details: Object.keys(formData.details).length > 0 ? formData.details : undefined,
      };

      await onSubmit(submitData);
    } catch (error) {
      console.error("Form submission error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Update form field with validation
  const updateField = (field: keyof EventFormData, value: any) => {
    // Handle clearing of optional fields
    const processedValue = value === undefined ? "" : value;
    setFormData(prev => ({ ...prev, [field]: processedValue }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Update details field with validation
  const updateDetails = (field: string, value: any) => {
    // Handle clearing of optional fields
    const processedValue = value === undefined ? "" : value;
    setFormData(prev => ({
      ...prev,
      details: { ...prev.details, [field]: processedValue }
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Get type options based on category
  const getTypeOptions = (category: string) => {
    const typeOptions: Record<string, string[]> = {
      Flight: ["Departure", "Arrival", "Connection"],
      Lodging: ["Check-in", "Check-out", "Room Service"],
      Activity: ["Food/Drink", "Sightseeing", "Entertainment", "Shopping", "Tour"],
      Cruise: ["Embarkation", "Disembarkation", "Port", "Sea Day"],
      Transport: ["Pickup", "Drop-off", "Transfer"],
      Info: ["General", "Important", "Emergency"],
    };
    return typeOptions[category] || [];
  };

  return (
    <form onSubmit={handleSubmit} className="p-6 space-y-6">

      {/* Event Category & Type Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-gray-50 rounded-lg p-4 space-y-4">
          <div className="flex items-center gap-2 mb-3">
            <Info className="h-4 w-4 text-blue-600" />
            <Text className="text-sm font-medium text-gray-900">Event Classification</Text>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Category *</label>
              <Select
                value={formData.category}
                onValueChange={(value) => updateField("category", value)}
              >
                <Select.Trigger>
                  <Select.Value placeholder="Select category..." />
                </Select.Trigger>
                <Select.Content>
                  <Select.Item value="Flight">✈️ Flight</Select.Item>
                  <Select.Item value="Lodging">🛏️ Lodging</Select.Item>
                  <Select.Item value="Activity">🎯 Activity</Select.Item>
                  <Select.Item value="Cruise">🚢 Cruise</Select.Item>
                  <Select.Item value="Transport">🚗 Transport</Select.Item>
                  <Select.Item value="Info">ℹ️ Info</Select.Item>
                </Select.Content>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Type</label>
              <div className="flex gap-2">
                <Select
                  value={formData.type || undefined}
                  onValueChange={(value) => updateField("type", value)}
                >
                  <Select.Trigger className="flex-1">
                    <Select.Value placeholder="Select type..." />
                  </Select.Trigger>
                  <Select.Content>
                    {getTypeOptions(formData.category).map(type => (
                      <Select.Item key={type} value={type}>{type}</Select.Item>
                    ))}
                  </Select.Content>
                </Select>
                {formData.type && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="small"
                    onClick={() => updateField("type", "")}
                    className="px-2"
                  >
                    Clear
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Basic Information Section */}
        <div className="bg-green-50 rounded-lg p-4 space-y-4">
          <div className="flex items-center gap-2 mb-3">
            <Calendar className="h-4 w-4 text-green-600" />
            <Text className="text-sm font-medium text-gray-900">Basic Information</Text>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Event Title *</label>
            <Input
              placeholder="Enter a descriptive title for your event"
              value={formData.title}
              onChange={(e) => updateField("title", e.target.value)}
              required
              className={`text-base ${errors.title ? 'border-red-500' : ''}`}
            />
            {errors.title && (
              <Text className="text-xs text-red-600 mt-1">{errors.title}</Text>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Description & Notes</label>
            <Textarea
              placeholder="Add detailed description, special instructions, or important notes..."
              value={formData.notes}
              onChange={(e) => updateField("notes", e.target.value)}
              rows={3}
              className="text-sm"
            />
          </div>
        </div>
      </div>

      {/* Timing Section */}
      <TimePicker
        startTime={formData.start_time}
        endTime={formData.end_time}
        duration={formData.duration}
        timezone={formData.timezone}
        onStartTimeChange={(time) => updateField("start_time", time)}
        onEndTimeChange={(time) => updateField("end_time", time)}
        onDurationChange={(duration) => updateField("duration", duration)}
        onTimezoneChange={(timezone) => updateField("timezone", timezone)}
      />

      {/* Category-Specific Details */}
      {(formData.category === "Flight" || formData.category === "Lodging" || formData.category === "Activity" || formData.category === "Cruise") && (
        <div className="bg-green-50 rounded-lg p-4 space-y-4">
          <div className="flex items-center gap-2 mb-3">
            <span className="text-lg">
              {formData.category === "Flight" && "✈️"}
              {formData.category === "Lodging" && "🛏️"}
              {formData.category === "Activity" && "🎯"}
              {formData.category === "Cruise" && "🚢"}
            </span>
            <Text className="text-sm font-medium text-gray-900">
              {formData.category} Details
            </Text>
          </div>

          {formData.category === "Flight" && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Flight Number</label>
                <Input
                  placeholder="e.g., AA123"
                  value={formData.details.flight_number || ""}
                  onChange={(e) => updateDetails("flight_number", e.target.value)}
                  className={errors.flight_number ? 'border-red-500' : ''}
                />
                {errors.flight_number && (
                  <Text className="text-xs text-red-600 mt-1">{errors.flight_number}</Text>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Airline</label>
                <Input
                  placeholder="e.g., American Airlines"
                  value={formData.details.airline || ""}
                  onChange={(e) => updateDetails("airline", e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Departure Airport</label>
                <Input
                  placeholder="e.g., JFK"
                  value={formData.details.departure_airport || ""}
                  onChange={(e) => updateDetails("departure_airport", e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Arrival Airport</label>
                <Input
                  placeholder="e.g., LAX"
                  value={formData.details.arrival_airport || ""}
                  onChange={(e) => updateDetails("arrival_airport", e.target.value)}
                />
              </div>
            </div>
          )}

          {formData.category === "Lodging" && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Hotel Name</label>
                <Input
                  placeholder="Hotel name"
                  value={formData.details.hotel_name || ""}
                  onChange={(e) => updateDetails("hotel_name", e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Room Number</label>
                <Input
                  placeholder="Room number"
                  value={formData.details.room_number || ""}
                  onChange={(e) => updateDetails("room_number", e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Check-in Time</label>
                <Input
                  type="time"
                  value={formData.details.checkin_time || ""}
                  onChange={(e) => updateDetails("checkin_time", e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Check-out Time</label>
                <Input
                  type="time"
                  value={formData.details.checkout_time || ""}
                  onChange={(e) => updateDetails("checkout_time", e.target.value)}
                />
              </div>
            </div>
          )}

          {formData.category === "Activity" && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Activity Provider</label>
                <Input
                  placeholder="e.g., Local Tours Inc."
                  value={formData.details.provider || ""}
                  onChange={(e) => updateDetails("provider", e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Difficulty Level</label>
                <div className="flex gap-2">
                  <Select
                    value={formData.details.difficulty || undefined}
                    onValueChange={(value) => updateDetails("difficulty", value)}
                  >
                    <Select.Trigger className="flex-1">
                      <Select.Value placeholder="Select difficulty..." />
                    </Select.Trigger>
                    <Select.Content>
                      <Select.Item value="Easy">Easy</Select.Item>
                      <Select.Item value="Moderate">Moderate</Select.Item>
                      <Select.Item value="Challenging">Challenging</Select.Item>
                    </Select.Content>
                  </Select>
                  {formData.details.difficulty && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="small"
                      onClick={() => updateDetails("difficulty", "")}
                      className="px-2"
                    >
                      Clear
                    </Button>
                  )}
                </div>
              </div>
            </div>
          )}

          {formData.category === "Cruise" && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Port Name</label>
                <Input
                  placeholder="e.g., Port of Miami"
                  value={formData.details.port_name || ""}
                  onChange={(e) => updateDetails("port_name", e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Deck/Location</label>
                <Input
                  placeholder="e.g., Deck 7, Pool Area"
                  value={formData.details.deck_location || ""}
                  onChange={(e) => updateDetails("deck_location", e.target.value)}
                />
              </div>
            </div>
          )}
        </div>
      )}

      {/* Location & Pricing Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Location */}
        <div className="bg-orange-50 rounded-lg p-4 space-y-4">
          <div className="flex items-center gap-2 mb-3">
            <MapPin className="h-4 w-4 text-orange-600" />
            <Text className="text-sm font-medium text-gray-900">Location Details</Text>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
            <Input
              placeholder="Enter specific location or address"
              value={formData.details.location || ""}
              onChange={(e) => updateDetails("location", e.target.value)}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Meeting Point</label>
            <Input
              placeholder="Where to meet (optional)"
              value={formData.details.meeting_point || ""}
              onChange={(e) => updateDetails("meeting_point", e.target.value)}
            />
          </div>
        </div>

        {/* Pricing */}
        <div className="bg-purple-50 rounded-lg p-4 space-y-4">
          <div className="flex items-center gap-2 mb-3">
            <DollarSign className="h-4 w-4 text-purple-600" />
            <Text className="text-sm font-medium text-gray-900">Pricing Information</Text>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Price</label>
              <Input
                type="number"
                placeholder="0.00"
                value={formData.price || ""}
                onChange={(e) => updateField("price", e.target.value ? parseFloat(e.target.value) : null)}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Currency</label>
              <Select
                value={formData.currency}
                onValueChange={(value) => updateField("currency", value)}
              >
                <Select.Trigger>
                  <Select.Value placeholder="Select currency..." />
                </Select.Trigger>
                <Select.Content>
                  <Select.Item value="USD">USD ($)</Select.Item>
                  <Select.Item value="EUR">EUR (€)</Select.Item>
                  <Select.Item value="GBP">GBP (£)</Select.Item>
                  <Select.Item value="CHF">CHF</Select.Item>
                </Select.Content>
              </Select>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Price Type</label>
            <div className="flex gap-2">
              <Select
                value={formData.details.price_type || undefined}
                onValueChange={(value) => updateDetails("price_type", value)}
              >
                <Select.Trigger className="flex-1">
                  <Select.Value placeholder="Select price type..." />
                </Select.Trigger>
                <Select.Content>
                  <Select.Item value="per_person">Per Person</Select.Item>
                  <Select.Item value="per_group">Per Group</Select.Item>
                  <Select.Item value="total">Total Cost</Select.Item>
                  <Select.Item value="included">Included</Select.Item>
                </Select.Content>
              </Select>
              {formData.details.price_type && (
                <Button
                  type="button"
                  variant="ghost"
                  size="small"
                  onClick={() => updateDetails("price_type", "")}
                  className="px-2"
                >
                  Clear
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* People & Capacity */}
      <div className="bg-indigo-50 rounded-lg p-4 space-y-4">
        <div className="flex items-center gap-2 mb-3">
          <Users className="h-4 w-4 text-indigo-600" />
          <Text className="text-sm font-medium text-gray-900">Participants & Capacity</Text>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Max Capacity</label>
            <Input
              type="number"
              placeholder="e.g., 20"
              value={formData.details.max_capacity || ""}
              onChange={(e) => updateDetails("max_capacity", e.target.value)}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Age Restrictions</label>
            <Input
              placeholder="e.g., 18+, All ages"
              value={formData.details.age_restrictions || ""}
              onChange={(e) => updateDetails("age_restrictions", e.target.value)}
            />
          </div>
        </div>
      </div>

      {/* Media & Attachments Section */}
      <div className="space-y-6">
        <div className="flex items-center gap-2 mb-4">
          <span className="text-lg">📎</span>
          <Text className="text-sm font-medium text-gray-900">Media & Documents</Text>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Media Upload */}
          <div className="bg-gray-50 rounded-lg p-4">
            <MediaUpload
              label="Images & Videos"
              value={formData.media}
              onChange={(files) => updateField("media", files)}
              accept="image/*,video/*"
              maxFiles={5}
            />
          </div>

          {/* Attachments Upload */}
          <div className="bg-gray-50 rounded-lg p-4">
            <MediaUpload
              label="Documents & Attachments"
              value={formData.attachments}
              onChange={(files) => updateField("attachments", files)}
              accept=".pdf,.doc,.docx,.txt"
              maxFiles={3}
            />
          </div>
        </div>
      </div>

      {/* Form Actions */}
      <div className="flex justify-between items-center pt-6 border-t border-gray-200">
        <div className="flex items-center gap-2">
          <Text className="text-xs text-gray-500">
            * Required fields
          </Text>
        </div>

        <div className="flex gap-3">
          <Button type="button" variant="secondary" onClick={onCancel} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button
            type="submit"
            className="bg-blue-600 hover:bg-blue-700"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                {initialData ? "Updating..." : "Creating..."}
              </div>
            ) : (
              initialData ? "Update Event" : "Create Event"
            )}
          </Button>
        </div>
      </div>
    </form>
  );
};

export default EventForm;

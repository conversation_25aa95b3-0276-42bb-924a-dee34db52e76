import { MedusaContainer } from "@camped-ai/framework/types";
import bookingModuleLoader from "./booking-module";
import addOnServiceModuleLoader from "./add-on-service-module";
import supplierProductsServicesModuleLoader from "./supplier-products-services-module";
import rbacModuleLoader from "./rbac-module";
import bookingAddOnServiceLoader from "./booking-add-on-service";
import itineraryServiceLoader from "./itinerary-service";
import conciergeManagementModuleLoader from "./concierge-management-module";

export default async (container: MedusaContainer): Promise<void> => {
  // Load all modules
  await bookingModuleLoader(container);
  await addOnServiceModuleLoader(container);
  await supplierProductsServicesModuleLoader(container);
  await rbacModuleLoader(container);
  await bookingAddOnServiceLoader(container);
  await itineraryServiceLoader(container);
  await conciergeManagementModuleLoader(container);
};

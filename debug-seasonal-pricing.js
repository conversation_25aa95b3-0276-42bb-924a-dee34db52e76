/**
 * Debug script to check existing seasonal pricing rules
 * Run this in your browser console on the admin page
 */

async function debugSeasonalPricing() {
  const roomConfigId = 'prod_01JWR26K8BNVDX0A1KKETHSTB6';
  const currencyCode = 'GBP';
  
  console.log('🔍 Debugging seasonal pricing for room config:', roomConfigId);
  console.log('💰 Currency:', currencyCode);
  
  try {
    // 1. Check existing seasonal pricing for this room config
    console.log('\n1️⃣ Fetching existing seasonal pricing...');
    const seasonalResponse = await fetch(
      `/admin/hotel-management/room-configs/${roomConfigId}/seasonal-pricing?currency_code=${currencyCode}`
    );
    
    if (seasonalResponse.ok) {
      const seasonalData = await seasonalResponse.json();
      console.log('📊 Existing seasonal pricing:', seasonalData);
      
      if (seasonalData.seasonal_prices && seasonalData.seasonal_prices.length > 0) {
        console.log(`\n🎯 Found ${seasonalData.seasonal_prices.length} seasonal pricing groups:`);
        seasonalData.seasonal_prices.forEach((season, index) => {
          console.log(`\n   Season ${index + 1}:`);
          console.log(`   📅 Name: "${season.name || 'UNNAMED'}"`);
          console.log(`   📅 Start: ${season.start_date}`);
          console.log(`   📅 End: ${season.end_date}`);
          console.log(`   💰 Currency: ${season.currency_code}`);
          console.log(`   🏷️  ID: ${season.id}`);
          
          if (season.weekday_rules && season.weekday_rules.length > 0) {
            console.log(`   📋 Rules: ${season.weekday_rules.length} pricing rules`);
            season.weekday_rules.forEach((rule, ruleIndex) => {
              console.log(`      Rule ${ruleIndex + 1}: Occupancy ${rule.occupancy_type_id}, Meal ${rule.meal_plan_id}`);
            });
          }
        });
      } else {
        console.log('✅ No existing seasonal pricing found');
      }
    } else {
      console.error('❌ Failed to fetch seasonal pricing:', await seasonalResponse.text());
    }
    
    // 2. Try to create a test season to see the exact error
    console.log('\n2️⃣ Testing season creation to see validation error...');
    const testSeasonData = {
      currency_code: currencyCode,
      name: "Test Season Debug",
      start_date: "2025-07-22",
      end_date: "2025-07-29",
      weekday_rules: []
    };
    
    const testResponse = await fetch(
      `/admin/hotel-management/room-configs/${roomConfigId}/seasonal-pricing/bulk`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testSeasonData)
      }
    );
    
    if (!testResponse.ok) {
      const errorData = await testResponse.json();
      console.log('❌ Validation error details:', errorData);
    } else {
      console.log('✅ Test season creation succeeded (this is unexpected!)');
    }
    
    // 3. Check comprehensive pricing data
    console.log('\n3️⃣ Checking comprehensive pricing data...');
    const hotelId = 'your-hotel-id'; // You'll need to replace this
    const comprehensiveResponse = await fetch(
      `/admin/hotel-management/hotels/${hotelId}/pricing/comprehensive?currency_code=${currencyCode}`
    );
    
    if (comprehensiveResponse.ok) {
      const comprehensiveData = await comprehensiveResponse.json();
      console.log('📊 Comprehensive pricing seasonal periods:', comprehensiveData.seasonal_periods);
    }
    
  } catch (error) {
    console.error('💥 Error during debugging:', error);
  }
}

// Run the debug function
console.log('🚀 Starting seasonal pricing debug...');
debugSeasonalPricing();
